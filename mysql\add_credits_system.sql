-- 添加點數系統到現有數據庫
-- KMS Receipt Maker Credits System Migration

-- 1. 為users表添加credits欄位
ALTER TABLE users ADD COLUMN credits INT DEFAULT 10 COMMENT '用戶點數，新用戶默認10點';

-- 2. 創建credit_transactions表記錄點數使用歷史
CREATE TABLE IF NOT EXISTS credit_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    transaction_type ENUM('earn', 'spend', 'admin_adjust') NOT NULL COMMENT '交易類型：獲得、消費、管理員調整',
    amount INT NOT NULL COMMENT '點數數量，正數為獲得，負數為消費',
    description VARCHAR(255) NOT NULL COMMENT '交易描述',
    reference_type VARCHAR(50) NULL COMMENT '關聯類型（如receipt、registration等）',
    reference_id INT NULL COMMENT '關聯ID',
    balance_after INT NOT NULL COMMENT '交易後餘額',
    created_by INT NULL COMMENT '操作者ID（管理員調整時使用）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='點數交易記錄表';

-- 3. 為現有用戶設置默認點數（如果credits欄位為NULL）
UPDATE users SET credits = 10 WHERE credits IS NULL;

-- 4. 為現有用戶創建初始點數記錄
INSERT INTO credit_transactions (user_id, transaction_type, amount, description, balance_after)
SELECT 
    id as user_id,
    'earn' as transaction_type,
    10 as amount,
    'System Initialization Bonus Credits' as description,
    10 as balance_after
FROM users 
WHERE NOT EXISTS (
    SELECT 1 FROM credit_transactions WHERE credit_transactions.user_id = users.id
);

-- 完成遷移
SELECT 'Credits system migration completed successfully!' as status;