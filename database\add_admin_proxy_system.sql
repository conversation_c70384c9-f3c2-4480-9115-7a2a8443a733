-- =====================================================
-- 添加管理員代理登入系統相關表
-- KMS Receipt Maker - Admin Proxy Login System
-- =====================================================

USE `kms_receipt_maker`;

-- =====================================================
-- Admin proxy logs table (admin_proxy_logs)
-- 記錄管理員代理登入的所有操作日誌
-- =====================================================

CREATE TABLE IF NOT EXISTS `admin_proxy_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Log ID',
    `admin_id` int(11) NOT NULL COMMENT 'Admin user ID who performed the action',
    `target_user_id` int(11) NOT NULL COMMENT 'Target user ID that was accessed',
    `action` varchar(50) NOT NULL COMMENT 'Action performed (proxy_login, return_to_admin)',
    `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP address of the admin',
    `user_agent` text DEFAULT NULL COMMENT 'User agent string',
    `session_duration` int(11) DEFAULT NULL COMMENT 'Session duration in seconds (for return_to_admin)',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Action timestamp',
    
    PRIMARY KEY (`id`),
    KEY `idx_admin_id` (`admin_id`),
    KEY `idx_target_user_id` (`target_user_id`),
    KEY `idx_action` (`action`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_admin_target` (`admin_id`, `target_user_id`),
    
    FOREIGN KEY (`admin_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`target_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Admin proxy login logs table';

-- =====================================================
-- Admin proxy settings table (admin_proxy_settings)
-- 存儲代理登入系統的配置設置
-- =====================================================

CREATE TABLE IF NOT EXISTS `admin_proxy_settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Setting ID',
    `setting_key` varchar(100) NOT NULL COMMENT 'Setting key',
    `setting_value` text DEFAULT NULL COMMENT 'Setting value',
    `description` varchar(255) DEFAULT NULL COMMENT 'Setting description',
    `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Is active',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Created time',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated time',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_setting_key` (`setting_key`),
    KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Admin proxy settings table';

-- =====================================================
-- 插入預設代理登入配置
-- =====================================================

INSERT INTO `admin_proxy_settings` (`setting_key`, `setting_value`, `description`) VALUES
('enable_proxy_login', '1', 'Enable admin proxy login functionality'),
('max_proxy_session_duration', '3600', 'Maximum proxy session duration in seconds (1 hour)'),
('log_proxy_actions', '1', 'Log all proxy login actions'),
('require_confirmation', '1', 'Require confirmation before proxy login'),
('allowed_proxy_user_types', 'member', 'User types that can be proxy logged into'),
('proxy_session_warning_time', '300', 'Show warning when proxy session expires in X seconds'),
('auto_return_on_inactivity', '1', 'Automatically return to admin on inactivity'),
('inactivity_timeout', '1800', 'Inactivity timeout in seconds (30 minutes)')
ON DUPLICATE KEY UPDATE 
    `setting_value` = VALUES(`setting_value`),
    `updated_at` = CURRENT_TIMESTAMP;

-- =====================================================
-- 創建視圖：代理登入統計
-- =====================================================

CREATE OR REPLACE VIEW `admin_proxy_stats` AS
SELECT 
    a.id as admin_id,
    a.username as admin_username,
    COUNT(DISTINCT apl.target_user_id) as total_users_accessed,
    COUNT(CASE WHEN apl.action = 'proxy_login' THEN 1 END) as total_proxy_logins,
    COUNT(CASE WHEN apl.action = 'return_to_admin' THEN 1 END) as total_returns,
    MAX(apl.created_at) as last_proxy_login,
    COUNT(CASE WHEN apl.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as logins_last_24h,
    COUNT(CASE WHEN apl.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as logins_last_7d
FROM users a
LEFT JOIN admin_proxy_logs apl ON a.id = apl.admin_id
WHERE a.user_type = 'admin'
GROUP BY a.id, a.username;

-- =====================================================
-- 創建視圖：用戶被代理登入統計
-- =====================================================

CREATE OR REPLACE VIEW `user_proxy_access_stats` AS
SELECT 
    u.id as user_id,
    u.username as username,
    u.email as email,
    COUNT(CASE WHEN apl.action = 'proxy_login' THEN 1 END) as times_accessed,
    COUNT(DISTINCT apl.admin_id) as accessed_by_admins,
    MAX(apl.created_at) as last_accessed,
    MIN(apl.created_at) as first_accessed,
    COUNT(CASE WHEN apl.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as accessed_last_30d
FROM users u
LEFT JOIN admin_proxy_logs apl ON u.id = apl.target_user_id
WHERE u.user_type = 'member'
GROUP BY u.id, u.username, u.email;

-- =====================================================
-- 創建索引以提高查詢性能
-- =====================================================

-- 為日誌表添加複合索引
ALTER TABLE `admin_proxy_logs` 
ADD INDEX IF NOT EXISTS `idx_admin_action_date` (`admin_id`, `action`, `created_at`),
ADD INDEX IF NOT EXISTS `idx_target_date` (`target_user_id`, `created_at`);

-- =====================================================
-- 創建觸發器：自動計算會話持續時間
-- =====================================================

DELIMITER $$

CREATE TRIGGER IF NOT EXISTS `calculate_session_duration` 
BEFORE INSERT ON `admin_proxy_logs`
FOR EACH ROW
BEGIN
    IF NEW.action = 'return_to_admin' THEN
        -- 查找對應的 proxy_login 記錄
        SET @login_time = (
            SELECT created_at 
            FROM admin_proxy_logs 
            WHERE admin_id = NEW.admin_id 
            AND target_user_id = NEW.target_user_id 
            AND action = 'proxy_login' 
            ORDER BY created_at DESC 
            LIMIT 1
        );
        
        -- 計算會話持續時間
        IF @login_time IS NOT NULL THEN
            SET NEW.session_duration = TIMESTAMPDIFF(SECOND, @login_time, NEW.created_at);
        END IF;
    END IF;
END$$

DELIMITER ;

COMMIT;
