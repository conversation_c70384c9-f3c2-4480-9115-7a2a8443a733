<?php
try {
    $pdo = new PDO('mysql:host=localhost;dbname=kms_receipt_maker;charset=utf8', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 創建admin用戶
    $username = 'admin';
    $password = 'admin123';
    $email = '<EMAIL>';
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    
    // 檢查admin用戶是否已存在
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
    $stmt->execute([$username]);
    
    if ($stmt->rowCount() > 0) {
        // 更新現有admin用戶的密碼
        $stmt = $pdo->prepare("UPDATE users SET password = ?, email = ?, user_type = 'admin', credits = 100, status = 'active', email_verified = 1 WHERE username = ?");
        $stmt->execute([$hashed_password, $email, $username]);
        echo "<p style='color: green;'>✓ Admin user updated successfully!</p>";
    } else {
        // 創建新的admin用戶
        $stmt = $pdo->prepare("INSERT INTO users (username, password, email, user_type, credits, status, email_verified) VALUES (?, ?, ?, 'admin', 100, 'active', 1)");
        $stmt->execute([$username, $hashed_password, $email]);
        echo "<p style='color: green;'>✓ Admin user created successfully!</p>";
    }
    
    // 顯示所有用戶
    $stmt = $pdo->query("SELECT id, username, email, user_type, credits, status FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<h3>All Users:</h3>";
    foreach ($users as $user) {
        echo "<p>ID: {$user['id']}, Username: {$user['username']}, Email: {$user['email']}, Type: {$user['user_type']}, Credits: {$user['credits']}, Status: {$user['status']}</p>";
    }
    
    // 測試登錄
    echo "<h3>Login Test:</h3>";
    $test_username = 'admin';
    $test_password = 'admin123';
    
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? OR email = ?");
    $stmt->execute([$test_username, $test_username]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user && password_verify($test_password, $user['password'])) {
        echo "<p style='color: green;'>✓ Login test successful for admin!</p>";
        echo "<p>User ID: {$user['id']}, Type: {$user['user_type']}, Credits: {$user['credits']}</p>";
    } else {
        echo "<p style='color: red;'>✗ Login test failed!</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}
?>