<?php
/**
 * 試用系統管理器
 * KMS Receipt Maker - Trial System Manager
 */

class TrialManager {
    private $db;
    private $conn;
    
    public function __construct() {
        require_once 'DatabaseMySQLi.php';
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }
    
    /**
     * 檢查用戶是否為試用用戶
     */
    public function isTrialUser($userId) {
        $stmt = $this->conn->prepare("SELECT is_trial_user FROM users WHERE id = ?");
        $stmt->bind_param('i', $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
        
        return $user ? (bool)$user['is_trial_user'] : false;
    }
    
    /**
     * 獲取用戶試用信息
     */
    public function getUserTrialInfo($userId) {
        $stmt = $this->conn->prepare("
            SELECT 
                is_trial_user,
                trial_start_date,
                trial_end_date,
                trial_days,
                trial_extended_days,
                trial_status,
                paid_member_since,
                DATEDIFF(trial_end_date, NOW()) as days_remaining,
                CASE 
                    WHEN trial_end_date < NOW() THEN 'expired'
                    WHEN DATEDIFF(trial_end_date, NOW()) <= 1 THEN 'expires_today'
                    WHEN DATEDIFF(trial_end_date, NOW()) <= 3 THEN 'expires_soon'
                    WHEN DATEDIFF(trial_end_date, NOW()) <= 7 THEN 'expires_this_week'
                    ELSE 'active'
                END as expiry_status
            FROM users 
            WHERE id = ?
        ");
        $stmt->bind_param('i', $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        return $result->fetch_assoc();
    }
    
    /**
     * 檢查用戶是否可以使用完整功能
     */
    public function canUseFullFeatures($userId) {
        $trialInfo = $this->getUserTrialInfo($userId);
        
        if (!$trialInfo) return false;
        
        // 如果不是試用用戶，可以使用完整功能
        if (!$trialInfo['is_trial_user']) {
            return true;
        }
        
        // 如果是試用用戶但已轉為付費會員
        if ($trialInfo['trial_status'] === 'converted') {
            return true;
        }
        
        // 如果試用期已過期
        if ($trialInfo['trial_status'] === 'expired' || $trialInfo['days_remaining'] < 0) {
            return false;
        }
        
        // 試用期內不能使用完整功能
        return false;
    }
    
    /**
     * 延長用戶試用期
     */
    public function extendTrialPeriod($userId, $extensionDays, $adminId, $reason = '') {
        try {
            $this->conn->begin_transaction();
            
            // 獲取當前試用信息
            $trialInfo = $this->getUserTrialInfo($userId);
            if (!$trialInfo || !$trialInfo['is_trial_user']) {
                throw new Exception('User is not a trial user');
            }

            // 確保用戶有有效的試用結束日期
            if (empty($trialInfo['trial_end_date']) || $trialInfo['trial_end_date'] === '0000-00-00 00:00:00') {
                // 如果沒有試用結束日期，根據試用天數設置一個
                $trialDays = $trialInfo['trial_days'] ?: 14;
                $startDate = $trialInfo['trial_start_date'] ?: date('Y-m-d H:i:s');
                $endDate = date('Y-m-d H:i:s', strtotime($startDate . " + {$trialDays} days"));

                // 更新用戶的試用結束日期
                $updateStmt = $this->conn->prepare("UPDATE users SET trial_end_date = ? WHERE id = ?");
                $updateStmt->bind_param('si', $endDate, $userId);
                $updateStmt->execute();

                // 更新本地變量
                $trialInfo['trial_end_date'] = $endDate;
            }
            
            // 檢查最大延長限制
            $maxExtensions = $this->getTrialSetting('max_trial_extensions', 3);
            $maxExtensionDays = $this->getTrialSetting('max_extension_days', 30);
            
            if ($extensionDays > $maxExtensionDays) {
                throw new Exception("Cannot extend more than {$maxExtensionDays} days at once");
            }
            
            // 檢查用戶已有的延長次數
            $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM trial_extensions WHERE user_id = ?");
            $stmt->bind_param('i', $userId);
            $stmt->execute();
            $result = $stmt->get_result();
            $extensionCount = $result->fetch_assoc()['count'];
            
            if ($extensionCount >= $maxExtensions) {
                throw new Exception("User has reached maximum trial extensions ({$maxExtensions})");
            }
            
            // 計算新的結束日期
            $currentEndDate = $trialInfo['trial_end_date'];

            // 如果當前結束日期為空，使用當前時間作為基準
            if (empty($currentEndDate) || $currentEndDate === '0000-00-00 00:00:00') {
                $currentEndDate = date('Y-m-d H:i:s');
            }

            $newEndDate = date('Y-m-d H:i:s', strtotime($currentEndDate . " + {$extensionDays} days"));
            
            // 更新用戶試用信息
            $stmt = $this->conn->prepare("
                UPDATE users 
                SET trial_end_date = ?, 
                    trial_extended_days = trial_extended_days + ?,
                    trial_status = 'active',
                    updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->bind_param('sii', $newEndDate, $extensionDays, $userId);
            $stmt->execute();
            
            // 記錄延長操作
            $stmt = $this->conn->prepare("
                INSERT INTO trial_extensions 
                (user_id, extended_by, extension_days, reason, previous_end_date, new_end_date, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
            
            $stmt->bind_param('iiisssss', 
                $userId, 
                $adminId, 
                $extensionDays, 
                $reason, 
                $currentEndDate, 
                $newEndDate, 
                $ipAddress, 
                $userAgent
            );
            $stmt->execute();
            
            $this->conn->commit();
            
            return [
                'success' => true,
                'message' => "Trial extended by {$extensionDays} days",
                'new_end_date' => $newEndDate
            ];
            
        } catch (Exception $e) {
            $this->conn->rollback();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 將試用用戶轉為付費會員
     */
    public function convertTrialToPaid($userId, $adminId = null) {
        try {
            $this->conn->begin_transaction();
            
            // 更新用戶狀態
            $stmt = $this->conn->prepare("
                UPDATE users 
                SET is_trial_user = 0,
                    trial_status = 'converted',
                    paid_member_since = NOW(),
                    credits = 10,
                    updated_at = NOW()
                WHERE id = ? AND is_trial_user = 1
            ");
            $stmt->bind_param('i', $userId);
            $stmt->execute();
            
            if ($stmt->affected_rows === 0) {
                throw new Exception('User not found or not a trial user');
            }
            
            // 記錄轉換操作
            if ($adminId) {
                require_once __DIR__ . '/CreditManager.php';
                $creditManager = new CreditManager();
                $creditManager->addCredits($userId, 10, 'Converted from trial to paid member', 'trial_conversion', null, $adminId);
            }
            
            $this->conn->commit();
            
            return [
                'success' => true,
                'message' => 'User successfully converted to paid member'
            ];
            
        } catch (Exception $e) {
            $this->conn->rollback();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 獲取試用系統設置
     */
    public function getTrialSetting($key, $default = null) {
        $stmt = $this->conn->prepare("
            SELECT setting_value 
            FROM trial_settings 
            WHERE setting_key = ? AND is_active = 1
        ");
        $stmt->bind_param('s', $key);
        $stmt->execute();
        $result = $stmt->get_result();
        $setting = $result->fetch_assoc();
        
        return $setting ? $setting['setting_value'] : $default;
    }
    
    /**
     * 更新試用系統設置
     */
    public function updateTrialSetting($key, $value, $description = '') {
        $stmt = $this->conn->prepare("
            INSERT INTO trial_settings (setting_key, setting_value, description) 
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value),
                description = VALUES(description),
                updated_at = NOW()
        ");
        $stmt->bind_param('sss', $key, $value, $description);
        
        return $stmt->execute();
    }
    
    /**
     * 獲取試用用戶統計
     */
    public function getTrialStats() {
        $stmt = $this->conn->prepare("SELECT * FROM trial_user_stats");
        $stmt->execute();
        $result = $stmt->get_result();
        
        return $result->fetch_assoc();
    }
    
    /**
     * 獲取即將到期的試用用戶
     */
    public function getExpiringTrialUsers($limit = 50) {
        $stmt = $this->conn->prepare("
            SELECT * FROM expiring_trial_users 
            WHERE days_remaining <= 7 
            ORDER BY trial_end_date ASC 
            LIMIT ?
        ");
        $stmt->bind_param('i', $limit);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $users = [];
        while ($row = $result->fetch_assoc()) {
            $users[] = $row;
        }
        
        return $users;
    }
    
    /**
     * 獲取用戶的試用延長記錄
     */
    public function getUserTrialExtensions($userId) {
        $stmt = $this->conn->prepare("
            SELECT te.*, u.username as extended_by_username
            FROM trial_extensions te
            LEFT JOIN users u ON te.extended_by = u.id
            WHERE te.user_id = ?
            ORDER BY te.created_at DESC
        ");
        $stmt->bind_param('i', $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $extensions = [];
        while ($row = $result->fetch_assoc()) {
            $extensions[] = $row;
        }
        
        return $extensions;
    }
    
    /**
     * 檢查並發送試用期提醒
     */
    public function checkAndSendTrialReminders() {
        // 這個方法可以被定期任務調用來發送試用期提醒
        $expiringUsers = $this->getExpiringTrialUsers();
        
        foreach ($expiringUsers as $user) {
            $this->sendTrialReminder($user);
        }
    }
    
    /**
     * 發送試用期提醒（預留接口）
     */
    private function sendTrialReminder($user) {
        // 這裡可以集成郵件系統發送提醒
        // 暫時只記錄到數據庫
        $notificationType = '';
        
        if ($user['days_remaining'] <= 0) {
            $notificationType = 'expired';
        } elseif ($user['days_remaining'] <= 1) {
            $notificationType = '1_day';
        } elseif ($user['days_remaining'] <= 3) {
            $notificationType = '3_days';
        } elseif ($user['days_remaining'] <= 7) {
            $notificationType = '7_days';
        }
        
        if ($notificationType) {
            $stmt = $this->conn->prepare("
                INSERT INTO trial_notifications 
                (user_id, notification_type, trial_end_date) 
                VALUES (?, ?, ?)
            ");
            $stmt->bind_param('iss', $user['id'], $notificationType, $user['trial_end_date']);
            $stmt->execute();
        }
    }
}
?>
