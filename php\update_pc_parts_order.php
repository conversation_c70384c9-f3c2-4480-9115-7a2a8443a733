<?php
/**
 * 更新電腦零件排序
 * KMS PC Receipt Maker
 */

session_start();

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

// 檢查用戶是否已登入
if (!isset($_SESSION['user_id'])) {
    Response::errorAndExit('請先登入', 401);
}

$currentUserId = $_SESSION['user_id'];

// 只允許POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Response::errorAndExit('只允許POST請求', 405);
}

try {
    // 獲取JSON數據
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data || !isset($data['items']) || !is_array($data['items'])) {
        Response::errorAndExit('無效的請求數據');
    }
    
    $db = new Database();
    $db->beginTransaction();
    
    try {
        // 檢查權限並更新每個項目的排序
        foreach ($data['items'] as $item) {
            if (!isset($item['id']) || !isset($item['sort_order'])) {
                continue;
            }
            
            $itemId = intval($item['id']);
            
            // 檢查項目是否存在且屬於當前用戶或管理員
            $checkSql = "SELECT id, created_by FROM pc_parts WHERE id = ?";
            $existing = $db->fetch($checkSql, [$itemId]);
            
            if (!$existing) {
                continue; // 跳過不存在的項目
            }
            
            // 檢查權限：只有項目創建者可以修改排序（包括管理員也只能修改自己的項目）
            if ($existing['created_by'] != $currentUserId) {
                continue; // 跳過沒有權限的項目
            }
            
            $sql = "UPDATE pc_parts SET sort_order = ? WHERE id = ?";
            $db->update($sql, [
                intval($item['sort_order']),
                $itemId
            ]);
        }
        
        $db->commit();
        Response::successAndExit(null, '排序更新成功');
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log('Update PC parts order error: ' . $e->getMessage());
    Response::errorAndExit('更新排序失敗: ' . $e->getMessage());
}
?>