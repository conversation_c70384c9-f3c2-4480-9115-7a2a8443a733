-- =====================================================
-- 添加會員試用系統
-- KMS Receipt Maker - Trial System
-- =====================================================

USE `kms_receipt_maker`;

-- =====================================================
-- 1. 修改 users 表，添加試用相關字段
-- =====================================================

ALTER TABLE `users` 
ADD COLUMN `trial_start_date` datetime DEFAULT NULL COMMENT 'Trial start date',
ADD COLUMN `trial_end_date` datetime DEFAULT NULL COMMENT 'Trial end date',
ADD COLUMN `trial_days` int(11) DEFAULT 14 COMMENT 'Trial period in days',
ADD COLUMN `is_trial_user` tinyint(1) DEFAULT 1 COMMENT 'Is trial user (1=trial, 0=paid)',
ADD COLUMN `trial_extended_days` int(11) DEFAULT 0 COMMENT 'Extended trial days by admin',
ADD COLUMN `trial_status` enum('active','expired','converted','cancelled') DEFAULT 'active' COMMENT 'Trial status',
ADD COLUMN `paid_member_since` datetime DEFAULT NULL COMMENT 'Date when became paid member',
ADD COLUMN `trial_notifications_sent` int(11) DEFAULT 0 COMMENT 'Number of trial expiry notifications sent';

-- 為新字段添加索引
ALTER TABLE `users`
ADD INDEX `idx_trial_status` (`trial_status`),
ADD INDEX `idx_is_trial_user` (`is_trial_user`),
ADD INDEX `idx_trial_end_date` (`trial_end_date`),
ADD INDEX `idx_trial_start_date` (`trial_start_date`);

-- =====================================================
-- 2. 創建試用系統設置表
-- =====================================================

CREATE TABLE IF NOT EXISTS `trial_settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Setting ID',
    `setting_key` varchar(100) NOT NULL COMMENT 'Setting key',
    `setting_value` text DEFAULT NULL COMMENT 'Setting value',
    `description` varchar(255) DEFAULT NULL COMMENT 'Setting description',
    `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Is active',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Created time',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated time',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_setting_key` (`setting_key`),
    KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Trial system settings table';

-- =====================================================
-- 3. 創建試用期延長記錄表
-- =====================================================

CREATE TABLE IF NOT EXISTS `trial_extensions` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Extension ID',
    `user_id` int(11) NOT NULL COMMENT 'User ID',
    `extended_by` int(11) NOT NULL COMMENT 'Admin who extended the trial',
    `extension_days` int(11) NOT NULL COMMENT 'Number of days extended',
    `reason` text DEFAULT NULL COMMENT 'Reason for extension',
    `previous_end_date` datetime NOT NULL COMMENT 'Previous trial end date',
    `new_end_date` datetime NOT NULL COMMENT 'New trial end date',
    `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP address of admin',
    `user_agent` text DEFAULT NULL COMMENT 'User agent string',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Extension timestamp',
    
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_extended_by` (`extended_by`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Trial extension records table';

-- =====================================================
-- 4. 創建試用期通知記錄表
-- =====================================================

CREATE TABLE IF NOT EXISTS `trial_notifications` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Notification ID',
    `user_id` int(11) NOT NULL COMMENT 'User ID',
    `notification_type` enum('7_days','3_days','1_day','expired') NOT NULL COMMENT 'Notification type',
    `sent_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Sent timestamp',
    `email_sent` tinyint(1) DEFAULT 0 COMMENT 'Email notification sent',
    `in_app_shown` tinyint(1) DEFAULT 0 COMMENT 'In-app notification shown',
    `trial_end_date` datetime NOT NULL COMMENT 'Trial end date at time of notification',
    
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_notification_type` (`notification_type`),
    KEY `idx_sent_at` (`sent_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Trial notifications table';

-- =====================================================
-- 5. 插入預設試用系統配置
-- =====================================================

INSERT INTO `trial_settings` (`setting_key`, `setting_value`, `description`) VALUES
('default_trial_days', '14', 'Default trial period in days for new users'),
('enable_trial_system', '1', 'Enable trial system for new registrations'),
('trial_notification_7_days', '1', 'Send notification 7 days before trial expires'),
('trial_notification_3_days', '1', 'Send notification 3 days before trial expires'),
('trial_notification_1_day', '1', 'Send notification 1 day before trial expires'),
('trial_notification_expired', '1', 'Send notification when trial expires'),
('max_trial_extensions', '3', 'Maximum number of trial extensions per user'),
('max_extension_days', '30', 'Maximum days that can be extended at once'),
('auto_disable_expired_trials', '1', 'Automatically disable expired trial accounts'),
('trial_grace_period_hours', '24', 'Grace period in hours after trial expires')
ON DUPLICATE KEY UPDATE 
    `setting_value` = VALUES(`setting_value`),
    `updated_at` = CURRENT_TIMESTAMP;

-- =====================================================
-- 6. 創建視圖：試用用戶統計
-- =====================================================

CREATE OR REPLACE VIEW `trial_user_stats` AS
SELECT 
    COUNT(*) as total_trial_users,
    COUNT(CASE WHEN trial_status = 'active' THEN 1 END) as active_trials,
    COUNT(CASE WHEN trial_status = 'expired' THEN 1 END) as expired_trials,
    COUNT(CASE WHEN trial_status = 'converted' THEN 1 END) as converted_trials,
    COUNT(CASE WHEN trial_status = 'cancelled' THEN 1 END) as cancelled_trials,
    COUNT(CASE WHEN trial_end_date < NOW() AND trial_status = 'active' THEN 1 END) as overdue_trials,
    COUNT(CASE WHEN trial_end_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY) THEN 1 END) as expiring_soon
FROM users 
WHERE is_trial_user = 1;

-- =====================================================
-- 7. 創建視圖：即將到期的試用用戶
-- =====================================================

CREATE OR REPLACE VIEW `expiring_trial_users` AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.trial_start_date,
    u.trial_end_date,
    u.trial_days,
    u.trial_extended_days,
    u.trial_status,
    DATEDIFF(u.trial_end_date, NOW()) as days_remaining,
    CASE 
        WHEN u.trial_end_date < NOW() THEN 'Expired'
        WHEN DATEDIFF(u.trial_end_date, NOW()) <= 1 THEN 'Expires Today'
        WHEN DATEDIFF(u.trial_end_date, NOW()) <= 3 THEN 'Expires Soon'
        WHEN DATEDIFF(u.trial_end_date, NOW()) <= 7 THEN 'Expires This Week'
        ELSE 'Active'
    END as expiry_status
FROM users u
WHERE u.is_trial_user = 1 
AND u.trial_status = 'active'
AND u.trial_end_date IS NOT NULL
ORDER BY u.trial_end_date ASC;

-- =====================================================
-- 8. 創建觸發器：自動設置新用戶試用期
-- =====================================================

DELIMITER $$

CREATE TRIGGER `set_trial_period_on_registration` 
BEFORE INSERT ON `users`
FOR EACH ROW
BEGIN
    DECLARE default_days INT DEFAULT 14;
    
    -- 獲取預設試用天數
    SELECT setting_value INTO default_days 
    FROM trial_settings 
    WHERE setting_key = 'default_trial_days' AND is_active = 1
    LIMIT 1;
    
    -- 只對會員類型設置試用期
    IF NEW.user_type = 'member' THEN
        SET NEW.is_trial_user = 1;
        SET NEW.trial_days = default_days;
        SET NEW.trial_start_date = NOW();
        SET NEW.trial_end_date = DATE_ADD(NOW(), INTERVAL default_days DAY);
        SET NEW.trial_status = 'active';
        SET NEW.credits = 0; -- 試用期不給點數
    ELSE
        -- 管理員不是試用用戶
        SET NEW.is_trial_user = 0;
        SET NEW.trial_status = NULL;
    END IF;
END$$

DELIMITER ;

-- =====================================================
-- 9. 創建事件：自動處理過期試用
-- =====================================================

DELIMITER $$

CREATE EVENT IF NOT EXISTS `process_expired_trials`
ON SCHEDULE EVERY 1 HOUR
STARTS CURRENT_TIMESTAMP
DO
BEGIN
    -- 更新過期的試用用戶狀態
    UPDATE users 
    SET trial_status = 'expired'
    WHERE is_trial_user = 1 
    AND trial_status = 'active'
    AND trial_end_date < NOW();
    
    -- 可選：自動禁用過期試用帳號
    UPDATE users 
    SET status = 'inactive'
    WHERE is_trial_user = 1 
    AND trial_status = 'expired'
    AND trial_end_date < DATE_SUB(NOW(), INTERVAL 24 HOUR)
    AND EXISTS (
        SELECT 1 FROM trial_settings 
        WHERE setting_key = 'auto_disable_expired_trials' 
        AND setting_value = '1' 
        AND is_active = 1
    );
END$$

DELIMITER ;

-- =====================================================
-- 10. 更新現有用戶為付費會員
-- =====================================================

-- 將現有的活躍用戶設置為付費會員（不是試用用戶）
UPDATE users 
SET is_trial_user = 0, 
    trial_status = 'converted',
    paid_member_since = created_at
WHERE user_type = 'member' 
AND status = 'active'
AND is_trial_user IS NULL;

-- 確保管理員不是試用用戶
UPDATE users 
SET is_trial_user = 0, 
    trial_status = NULL
WHERE user_type = 'admin';

-- =====================================================
-- 11. 創建索引以提高查詢性能
-- =====================================================

-- 為試用相關查詢添加複合索引
ALTER TABLE `users` 
ADD INDEX IF NOT EXISTS `idx_trial_user_status` (`is_trial_user`, `trial_status`),
ADD INDEX IF NOT EXISTS `idx_trial_expiry` (`trial_end_date`, `trial_status`),
ADD INDEX IF NOT EXISTS `idx_user_type_trial` (`user_type`, `is_trial_user`);

-- 為試用延長記錄添加複合索引
ALTER TABLE `trial_extensions` 
ADD INDEX IF NOT EXISTS `idx_user_extension_date` (`user_id`, `created_at`),
ADD INDEX IF NOT EXISTS `idx_admin_extension` (`extended_by`, `created_at`);

-- 為試用通知記錄添加複合索引
ALTER TABLE `trial_notifications` 
ADD INDEX IF NOT EXISTS `idx_user_notification_type` (`user_id`, `notification_type`),
ADD INDEX IF NOT EXISTS `idx_notification_date` (`sent_at`, `notification_type`);

-- 啟用事件調度器（如果尚未啟用）
SET GLOBAL event_scheduler = ON;

COMMIT;
