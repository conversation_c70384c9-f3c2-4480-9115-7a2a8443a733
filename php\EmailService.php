<?php
/**
 * 郵件發送服務類
 * KMS Receipt Maker - Email Service
 * 
 * 支持 HTML 郵件發送，多語言模板，以及郵件驗證功能
 */

require_once 'config.php';
require_once 'DatabaseMySQLi.php';

class EmailService {
    private $fromEmail;
    private $fromName;
    private $smtpEnabled;
    private $db;
    
    public function __construct() {
        $this->fromEmail = COMPANY_EMAIL;
        $this->fromName = COMPANY_NAME;
        $this->smtpEnabled = false; // 可以在配置中啟用 SMTP
        
        try {
            $database = new Database();
            $this->db = $database->getConnection();
        } catch (Exception $e) {
            error_log("EmailService database connection failed: " . $e->getMessage());
        }
    }
    
    /**
     * 發送郵件驗證郵件
     * 
     * @param string $email 收件人郵箱
     * @param string $username 用戶名
     * @param string $token 驗證令牌
     * @param string $language 語言 ('en' 或 'zh')
     * @return bool 發送是否成功
     */
    public function sendEmailVerification($email, $username, $token, $language = 'en') {
        $verifyUrl = $this->getBaseUrl() . "/verify-email.php?token=" . urlencode($token);
        
        // 根據語言選擇模板
        if ($language === 'zh') {
            $subject = "驗證您的 {$this->fromName} 帳號";
            $template = $this->getChineseVerificationTemplate($username, $verifyUrl);
        } else {
            $subject = "Verify Your {$this->fromName} Account";
            $template = $this->getEnglishVerificationTemplate($username, $verifyUrl);
        }
        
        return $this->sendHtmlEmail($email, $subject, $template);
    }
    
    /**
     * 發送密碼重置郵件
     * 
     * @param string $email 收件人郵箱
     * @param string $username 用戶名
     * @param string $token 重置令牌
     * @param string $language 語言
     * @return bool 發送是否成功
     */
    public function sendPasswordReset($email, $username, $token, $language = 'en') {
        $resetUrl = $this->getBaseUrl() . "/reset-password.php?token=" . urlencode($token);
        
        if ($language === 'zh') {
            $subject = "重置您的 {$this->fromName} 密碼";
            $template = $this->getChinesePasswordResetTemplate($username, $resetUrl);
        } else {
            $subject = "Reset Your {$this->fromName} Password";
            $template = $this->getEnglishPasswordResetTemplate($username, $resetUrl);
        }
        
        return $this->sendHtmlEmail($email, $subject, $template);
    }
    
    /**
     * 發送 HTML 郵件
     * 
     * @param string $to 收件人郵箱
     * @param string $subject 郵件主題
     * @param string $htmlContent HTML 內容
     * @return bool 發送是否成功
     */
    private function sendHtmlEmail($to, $subject, $htmlContent) {
        try {
            // 設置郵件頭
            $headers = [
                'MIME-Version: 1.0',
                'Content-type: text/html; charset=UTF-8',
                "From: {$this->fromName} <{$this->fromEmail}>",
                "Reply-To: {$this->fromEmail}",
                'X-Mailer: PHP/' . phpversion()
            ];
            
            $headerString = implode("\r\n", $headers);
            
            // 發送郵件
            $result = mail($to, $subject, $htmlContent, $headerString);
            
            // 記錄郵件發送日誌
            $this->logEmailSent($to, $subject, $result);
            
            return $result;
            
        } catch (Exception $e) {
            error_log("Email sending failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 獲取基礎 URL
     */
    private function getBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $path = dirname($_SERVER['SCRIPT_NAME'] ?? '');
        
        return $protocol . '://' . $host . $path;
    }
    
    /**
     * 記錄郵件發送日誌
     */
    private function logEmailSent($to, $subject, $success) {
        if (!$this->db) return;
        
        try {
            $stmt = $this->db->prepare("
                INSERT INTO email_logs (recipient, subject, sent_at, success, created_at) 
                VALUES (?, ?, NOW(), ?, NOW())
            ");
            $stmt->bind_param('ssi', $to, $subject, $success ? 1 : 0);
            $stmt->execute();
        } catch (Exception $e) {
            error_log("Failed to log email: " . $e->getMessage());
        }
    }
    
    /**
     * 英文郵件驗證模板
     */
    private function getEnglishVerificationTemplate($username, $verifyUrl) {
        return $this->getEmailTemplate([
            'title' => 'Verify Your Email Address',
            'greeting' => "Hello {$username},",
            'message' => 'Thank you for registering with ' . $this->fromName . '. To complete your registration and activate your account, please click the button below to verify your email address.',
            'button_text' => 'Verify Email Address',
            'button_url' => $verifyUrl,
            'footer_text' => 'If you did not create an account, please ignore this email.',
            'expire_text' => 'This verification link will expire in 24 hours.',
            'manual_link_text' => 'If the button above doesn\'t work, copy and paste this link into your browser:',
            'language' => 'en'
        ]);
    }
    
    /**
     * 中文郵件驗證模板
     */
    private function getChineseVerificationTemplate($username, $verifyUrl) {
        return $this->getEmailTemplate([
            'title' => '驗證您的郵箱地址',
            'greeting' => "您好 {$username}，",
            'message' => '感謝您註冊 ' . $this->fromName . '。為了完成註冊並激活您的帳號，請點擊下方按鈕驗證您的郵箱地址。',
            'button_text' => '驗證郵箱地址',
            'button_url' => $verifyUrl,
            'footer_text' => '如果您沒有創建帳號，請忽略此郵件。',
            'expire_text' => '此驗證鏈接將在24小時後過期。',
            'manual_link_text' => '如果上方按鈕無法使用，請複製以下鏈接到瀏覽器中打開：',
            'language' => 'zh'
        ]);
    }
    
    /**
     * 英文密碼重置模板
     */
    private function getEnglishPasswordResetTemplate($username, $resetUrl) {
        return $this->getEmailTemplate([
            'title' => 'Reset Your Password',
            'greeting' => "Hello {$username},",
            'message' => 'We received a request to reset your password for your ' . $this->fromName . ' account. Click the button below to reset your password.',
            'button_text' => 'Reset Password',
            'button_url' => $resetUrl,
            'footer_text' => 'If you did not request a password reset, please ignore this email.',
            'expire_text' => 'This reset link will expire in 1 hour.',
            'manual_link_text' => 'If the button above doesn\'t work, copy and paste this link into your browser:',
            'language' => 'en'
        ]);
    }
    
    /**
     * 中文密碼重置模板
     */
    private function getChinesePasswordResetTemplate($username, $resetUrl) {
        return $this->getEmailTemplate([
            'title' => '重置您的密碼',
            'greeting' => "您好 {$username}，",
            'message' => '我們收到了重置您 ' . $this->fromName . ' 帳號密碼的請求。請點擊下方按鈕重置您的密碼。',
            'button_text' => '重置密碼',
            'button_url' => $resetUrl,
            'footer_text' => '如果您沒有請求重置密碼，請忽略此郵件。',
            'expire_text' => '此重置鏈接將在1小時後過期。',
            'manual_link_text' => '如果上方按鈕無法使用，請複製以下鏈接到瀏覽器中打開：',
            'language' => 'zh'
        ]);
    }
    
    /**
     * 通用郵件模板
     */
    private function getEmailTemplate($data) {
        $primaryColor = '#4a90e2';
        $backgroundColor = '#f8f9fa';
        
        return "
        <!DOCTYPE html>
        <html lang='{$data['language']}'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>{$data['title']}</title>
            <style>
                body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background-color: {$backgroundColor}; }
                .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
                .header { background: linear-gradient(135deg, {$primaryColor} 0%, #3a7bd5 100%); padding: 30px 20px; text-align: center; }
                .header h1 { color: #ffffff; margin: 0; font-size: 28px; font-weight: 600; }
                .content { padding: 40px 30px; }
                .greeting { font-size: 18px; color: #333333; margin-bottom: 20px; }
                .message { font-size: 16px; color: #555555; line-height: 1.6; margin-bottom: 30px; }
                .button { display: inline-block; background: linear-gradient(135deg, {$primaryColor} 0%, #3a7bd5 100%); color: #ffffff; text-decoration: none; padding: 15px 30px; border-radius: 8px; font-weight: 600; font-size: 16px; margin: 20px 0; }
                .button:hover { opacity: 0.9; }
                .footer { background-color: #f8f9fa; padding: 30px; text-align: center; border-top: 1px solid #e9ecef; }
                .footer-text { font-size: 14px; color: #6c757d; margin-bottom: 10px; }
                .manual-link { font-size: 12px; color: #6c757d; word-break: break-all; }
                .expire-notice { font-size: 14px; color: #dc3545; margin-top: 20px; font-weight: 500; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>{$this->fromName}</h1>
                </div>
                <div class='content'>
                    <div class='greeting'>{$data['greeting']}</div>
                    <div class='message'>{$data['message']}</div>
                    <div style='text-align: center;'>
                        <a href='{$data['button_url']}' class='button'>{$data['button_text']}</a>
                    </div>
                    <div class='expire-notice'>{$data['expire_text']}</div>
                </div>
                <div class='footer'>
                    <div class='footer-text'>{$data['footer_text']}</div>
                    <div class='manual-link'>
                        <p>{$data['manual_link_text']}</p>
                        <p>{$data['button_url']}</p>
                    </div>
                </div>
            </div>
        </body>
        </html>";
    }
}
?>
