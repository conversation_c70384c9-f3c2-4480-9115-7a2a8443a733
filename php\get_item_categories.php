<?php
/**
 * 獲取物品分類 API
 * KMS PC Receipt Maker
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

try {
    $db = new Database();
    
    // 檢查資料庫連接
    if (!$db->isConnected()) {
        throw new Exception('資料庫連接失敗');
    }
    
    // 獲取所有啟用的分類
    $sql = "SELECT id, name, name_en, description, icon, color, sort_order 
            FROM item_categories 
            WHERE is_active = 1 
            ORDER BY sort_order ASC, name ASC";
    
    $categories = $db->fetchAll($sql);
    
    // 格式化資料
    $formattedCategories = [];
    foreach ($categories as $category) {
        $formattedCategories[] = [
            'id' => (int)$category['id'],
            'name' => $category['name'],
            'name_en' => $category['name_en'],
            'description' => $category['description'],
            'icon' => $category['icon'] ?? '📦',
            'color' => $category['color'] ?? '#6c757d',
            'sort_order' => (int)$category['sort_order']
        ];
    }
    
    Response::successAndExit($formattedCategories, '分類資料獲取成功');
    
} catch (Exception $e) {
    error_log('Get item categories error: ' . $e->getMessage());
    Response::errorAndExit('獲取分類資料失敗: ' . $e->getMessage(), 500);
}
?>