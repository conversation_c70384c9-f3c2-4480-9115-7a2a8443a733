/**
 * 帳號刪除功能 JavaScript
 * KMS Receipt Maker - Account Deletion Manager
 */

class AccountDeletionManager {
    constructor() {
        this.modal = null;
        this.userInfo = null;
        this.dataStats = null;
        this.isLoading = false;
    }

    /**
     * 顯示帳號刪除確認彈窗
     */
    async showDeletionModal() {
        try {
            // 獲取用戶刪除信息
            const response = await fetch('php/self_delete_account.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=get_deletion_info'
            });

            const data = await response.json();
            
            if (data.success) {
                this.userInfo = data.data.user_info;
                this.dataStats = data.data.data_stats;
                this.createModal();
                this.showModal();
            } else {
                this.showNotification(data.message || 'Failed to load deletion info', 'error');
            }
        } catch (error) {
            console.error('Error loading deletion info:', error);
            this.showNotification('Network error occurred', 'error');
        }
    }

    /**
     * 創建刪除確認彈窗
     */
    createModal() {
        const modalHTML = `
            <div class="account-deletion-modal" id="accountDeletionModal">
                <div class="deletion-modal-content">
                    <div class="deletion-modal-header">
                        <span class="warning-icon">⚠️</span>
                        <h2 data-lang="delete_account_title">Delete Account</h2>
                        <div class="subtitle" data-lang="delete_account_subtitle">This action cannot be undone</div>
                    </div>
                    
                    <div class="deletion-modal-body">
                        <div class="warning-section">
                            <h3>
                                <span class="icon">🚨</span>
                                <span data-lang="warning_title">Warning</span>
                            </h3>
                            <ul class="warning-list">
                                <li>
                                    <span class="icon">🗑️</span>
                                    <span data-lang="warning_permanent">This action is permanent and cannot be undone</span>
                                </li>
                                <li>
                                    <span class="icon">📄</span>
                                    <span data-lang="warning_receipts">All your receipts will be permanently deleted</span>
                                </li>
                                <li>
                                    <span class="icon">⚙️</span>
                                    <span data-lang="warning_presets">All your presets and configurations will be lost</span>
                                </li>
                                <li>
                                    <span class="icon">💰</span>
                                    <span data-lang="warning_credits">All remaining credits will be forfeited</span>
                                </li>
                                <li>
                                    <span class="icon">🔒</span>
                                    <span data-lang="warning_access">You will lose access to your account immediately</span>
                                </li>
                            </ul>
                        </div>

                        <div class="data-stats">
                            <h3>
                                <span class="icon">📊</span>
                                <span data-lang="your_data_title">Your Data Summary</span>
                            </h3>
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <span class="stat-number">${this.dataStats.receipts}</span>
                                    <span class="stat-label" data-lang="receipts">Receipts</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number">${this.dataStats.presets}</span>
                                    <span class="stat-label" data-lang="presets">Presets</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number">${this.dataStats.configurations}</span>
                                    <span class="stat-label" data-lang="configurations">Configurations</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number">${this.dataStats.credit_transactions}</span>
                                    <span class="stat-label" data-lang="transactions">Transactions</span>
                                </div>
                            </div>
                        </div>

                        <div class="confirmation-section">
                            <h3>
                                <span class="icon">🔐</span>
                                <span data-lang="confirmation_required">Confirmation Required</span>
                            </h3>
                            
                            <div class="form-group">
                                <label for="deletePassword" data-lang="enter_password">Enter your password to confirm:</label>
                                <input type="password" id="deletePassword" placeholder="Password" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="deleteConfirmation" data-lang="type_delete">Type "DELETE" to confirm:</label>
                                <input type="text" id="deleteConfirmation" class="confirmation-input" placeholder="DELETE" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="deletion-modal-footer">
                        <button type="button" class="btn-cancel" onclick="accountDeletionManager.hideModal()" data-lang="cancel">
                            Cancel
                        </button>
                        <button type="button" class="btn-delete" id="confirmDeleteBtn" onclick="accountDeletionManager.confirmDeletion()" data-lang="delete_account">
                            Delete Account
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 移除現有的彈窗
        const existingModal = document.getElementById('accountDeletionModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新彈窗到頁面
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.modal = document.getElementById('accountDeletionModal');

        // 綁定事件
        this.bindEvents();
        
        // 應用語言翻譯
        if (typeof LanguageManager !== 'undefined') {
            LanguageManager.applyLanguage();
        }
    }

    /**
     * 綁定事件
     */
    bindEvents() {
        const passwordInput = document.getElementById('deletePassword');
        const confirmationInput = document.getElementById('deleteConfirmation');
        const deleteBtn = document.getElementById('confirmDeleteBtn');

        // 監聽輸入變化
        const checkInputs = () => {
            const password = passwordInput.value.trim();
            const confirmation = confirmationInput.value.trim();
            
            deleteBtn.disabled = !password || confirmation !== 'DELETE';
        };

        passwordInput.addEventListener('input', checkInputs);
        confirmationInput.addEventListener('input', checkInputs);

        // 點擊背景關閉彈窗
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.hideModal();
            }
        });

        // ESC 鍵關閉彈窗
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal && this.modal.style.display !== 'none') {
                this.hideModal();
            }
        });

        // 初始檢查
        checkInputs();
    }

    /**
     * 顯示彈窗
     */
    showModal() {
        if (this.modal) {
            this.modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
    }

    /**
     * 隱藏彈窗
     */
    hideModal() {
        if (this.modal) {
            this.modal.style.display = 'none';
            document.body.style.overflow = '';
        }
    }

    /**
     * 確認刪除帳號
     */
    async confirmDeletion() {
        if (this.isLoading) return;

        const password = document.getElementById('deletePassword').value.trim();
        const confirmation = document.getElementById('deleteConfirmation').value.trim();
        const deleteBtn = document.getElementById('confirmDeleteBtn');

        if (!password || confirmation !== 'DELETE') {
            this.showNotification('Please fill in all required fields correctly', 'error');
            return;
        }

        this.isLoading = true;
        deleteBtn.disabled = true;
        deleteBtn.classList.add('loading');

        try {
            const response = await fetch('php/self_delete_account.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=confirm_deletion&password=${encodeURIComponent(password)}&confirmation=${encodeURIComponent(confirmation)}`
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('Account deleted successfully. Redirecting...', 'success');
                
                // 延遲跳轉
                setTimeout(() => {
                    window.location.href = data.data.redirect_url;
                }, 2000);
            } else {
                this.showNotification(data.message || 'Failed to delete account', 'error');
            }
        } catch (error) {
            console.error('Error deleting account:', error);
            this.showNotification('Network error occurred', 'error');
        } finally {
            this.isLoading = false;
            deleteBtn.disabled = false;
            deleteBtn.classList.remove('loading');
        }
    }

    /**
     * 顯示通知
     */
    showNotification(message, type = 'info') {
        // 如果有全局通知函數，使用它
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else {
            // 否則使用 alert
            alert(message);
        }
    }
}

// 創建全局實例
const accountDeletionManager = new AccountDeletionManager();

// 全局函數供 HTML 調用
window.showAccountDeletionModal = () => {
    accountDeletionManager.showDeletionModal();
};

window.accountDeletionManager = accountDeletionManager;
