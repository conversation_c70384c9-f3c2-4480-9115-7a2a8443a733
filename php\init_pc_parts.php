<?php
/**
 * 初始化電腦零件數據
 * KMS PC Receipt Maker
 */

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $db = new Database();
    
    // 檢查是否已有數據
    $countSql = "SELECT COUNT(*) as count FROM pc_parts";
    $result = $db->fetch($countSql);
    
    if ($result['count'] > 0) {
        Response::successAndExit();
    }
    
    // 停用預設資料初始化：不插入任何預設零件，直接返回成功
    Response::successAndExit();

    // 預設電腦零件數據（已停用）
    $pcParts = [
        // CPU 處理器
        ['name' => 'Intel Core i5-12400F', 'category' => 'CPU', 'description' => 'Intel 第12代處理器，6核心12線程，基頻3.0GHz', 'default_price' => 6500.00],
        ['name' => 'Intel Core i7-12700K', 'category' => 'CPU', 'description' => 'Intel 第12代處理器，12核心20線程，基頻3.6GHz', 'default_price' => 12000.00],
        ['name' => 'AMD Ryzen 5 5600X', 'category' => 'CPU', 'description' => 'AMD Zen3架構，6核心12線程，基頻3.7GHz', 'default_price' => 7500.00],
        ['name' => 'AMD Ryzen 7 5800X', 'category' => 'CPU', 'description' => 'AMD Zen3架構，8核心16線程，基頻3.8GHz', 'default_price' => 11000.00],
        ['name' => 'Intel Core i9-12900K', 'category' => 'CPU', 'description' => 'Intel 第12代旗艦處理器，16核心24線程', 'default_price' => 18000.00],
        
        // 主機板
        ['name' => 'ASUS PRIME B550M-A', 'category' => 'Motherboard', 'description' => 'AMD B550晶片組，支援Ryzen 5000系列，mATX規格', 'default_price' => 3200.00],
        ['name' => 'MSI MAG B550 TOMAHAWK', 'category' => 'Motherboard', 'description' => 'AMD B550晶片組，ATX規格，支援PCIe 4.0', 'default_price' => 4500.00],
        ['name' => 'ASUS ROG STRIX Z690-E', 'category' => 'Motherboard', 'description' => 'Intel Z690晶片組，支援12代CPU，ATX規格', 'default_price' => 8500.00],
        ['name' => 'GIGABYTE B450M DS3H', 'category' => 'Motherboard', 'description' => 'AMD B450晶片組，mATX規格，經濟實用', 'default_price' => 2200.00],
        
        // 記憶體
        ['name' => 'Corsair Vengeance LPX 16GB DDR4-3200', 'category' => 'RAM', 'description' => '16GB DDR4記憶體，3200MHz，低延遲設計', 'default_price' => 2800.00],
        ['name' => 'G.SKILL Trident Z RGB 32GB DDR4-3600', 'category' => 'RAM', 'description' => '32GB DDR4記憶體，RGB燈效，高性能', 'default_price' => 5500.00],
        ['name' => 'Kingston FURY Beast 16GB DDR5-5600', 'category' => 'RAM', 'description' => '16GB DDR5記憶體，5600MHz，次世代規格', 'default_price' => 4200.00],
        ['name' => 'Corsair Vengeance LPX 8GB DDR4-3200', 'category' => 'RAM', 'description' => '8GB DDR4記憶體，3200MHz，單條', 'default_price' => 1400.00],
        
        // 顯示卡
        ['name' => 'NVIDIA GeForce RTX 3060', 'category' => 'GPU', 'description' => '12GB GDDR6，適合1080p遊戲，支援光線追蹤', 'default_price' => 15000.00],
        ['name' => 'NVIDIA GeForce RTX 3070', 'category' => 'GPU', 'description' => '8GB GDDR6，適合1440p遊戲，高性能', 'default_price' => 22000.00],
        ['name' => 'NVIDIA GeForce RTX 4080', 'category' => 'GPU', 'description' => '16GB GDDR6X，4K遊戲首選，最新架構', 'default_price' => 38000.00],
        ['name' => 'AMD Radeon RX 6600 XT', 'category' => 'GPU', 'description' => '8GB GDDR6，性價比之選，1080p高幀率', 'default_price' => 13500.00],
        ['name' => 'NVIDIA GeForce RTX 4090', 'category' => 'GPU', 'description' => '24GB GDDR6X，旗艦級顯卡，4K極致性能', 'default_price' => 55000.00],
        
        // 儲存裝置
        ['name' => 'Samsung 980 PRO 1TB NVMe SSD', 'category' => 'SSD', 'description' => '1TB NVMe SSD，PCIe 4.0，讀取速度7000MB/s', 'default_price' => 4500.00],
        ['name' => 'WD Black SN850 2TB NVMe SSD', 'category' => 'SSD', 'description' => '2TB NVMe SSD，高速讀寫，遊戲優化', 'default_price' => 8500.00],
        ['name' => 'Seagate Barracuda 2TB HDD', 'category' => 'SSD', 'description' => '2TB 7200RPM 傳統硬碟，大容量存儲', 'default_price' => 2200.00],
        ['name' => 'Samsung 970 EVO Plus 500GB', 'category' => 'SSD', 'description' => '500GB NVMe SSD，PCIe 3.0，高性價比', 'default_price' => 2800.00],
        ['name' => 'Crucial MX500 1TB SATA SSD', 'category' => 'SSD', 'description' => '1TB SATA SSD，2.5吋規格，穩定可靠', 'default_price' => 3200.00],
        
        // 電源供應器
        ['name' => 'Corsair RM750x 750W', 'category' => 'PSU', 'description' => '750W 80+ Gold認證，全模組化，十年保固', 'default_price' => 3800.00],
        ['name' => 'Seasonic Focus GX-850 850W', 'category' => 'PSU', 'description' => '850W 80+ Gold認證，十年保固，日系電容', 'default_price' => 4200.00],
        ['name' => 'EVGA SuperNOVA 1000 G5', 'category' => 'PSU', 'description' => '1000W 80+ Gold認證，適合高階配置', 'default_price' => 5500.00],
        ['name' => 'Cooler Master MWE 650W', 'category' => 'PSU', 'description' => '650W 80+ Bronze認證，經濟實用', 'default_price' => 2200.00],
        
        // 機殼
        ['name' => 'Fractal Design Define 7', 'category' => 'PC Case', 'description' => 'ATX靜音機殼，優秀散熱設計，模組化內裝', 'default_price' => 4500.00],
        ['name' => 'Corsair 4000D Airflow', 'category' => 'PC Case', 'description' => 'ATX機殼，優化風流設計，鋼化玻璃側板', 'default_price' => 3200.00],
        ['name' => 'NZXT H510 Elite', 'category' => 'PC Case', 'description' => 'ATX機殼，鋼化玻璃側板，RGB燈效', 'default_price' => 3800.00],
        ['name' => 'Cooler Master MasterBox Q300L', 'category' => 'PC Case', 'description' => 'mATX小型機殼，緊湊設計', 'default_price' => 1800.00],
        
        // 散熱器
        ['name' => 'Noctua NH-D15', 'category' => 'CPU Cooler', 'description' => '雙塔風冷散熱器，靜音高效，頂級性能', 'default_price' => 3200.00],
        ['name' => 'Corsair H100i RGB PLATINUM', 'category' => 'CPU Cooler', 'description' => '240mm一體式水冷，RGB燈效，高效散熱', 'default_price' => 4500.00],
        ['name' => 'be quiet! Dark Rock Pro 4', 'category' => 'CPU Cooler', 'description' => '雙塔風冷散熱器，極致靜音，德國工藝', 'default_price' => 2800.00],
        ['name' => 'Cooler Master Hyper 212', 'category' => 'CPU Cooler', 'description' => '塔式風冷散熱器，經濟實用，廣泛兼容', 'default_price' => 1200.00],
        ['name' => 'NZXT Kraken X63', 'category' => 'CPU Cooler', 'description' => '280mm一體式水冷，LCD顯示屏，高端配置', 'default_price' => 6500.00],
        
        // RGB 配件
        ['name' => 'Corsair RGB LED Strip', 'category' => 'MB RGB', 'description' => 'RGB燈條，可編程燈效，主機板同步', 'default_price' => 800.00],
        ['name' => 'ASUS Aura Sync RGB Kit', 'category' => 'MB RGB', 'description' => 'ASUS RGB套件，主機板燈效同步', 'default_price' => 1200.00],
        ['name' => 'MSI Mystic Light RGB', 'category' => 'GPU RGB', 'description' => 'MSI顯卡RGB燈效套件', 'default_price' => 600.00],
        ['name' => 'Corsair LL120 RGB Fan', 'category' => 'Fan RGB', 'description' => '120mm RGB風扇，雙環燈效', 'default_price' => 1500.00],
        
        // 其他配件
        ['name' => 'TP-Link AC1200 WiFi Card', 'category' => 'Other', 'description' => 'PCIe無線網卡，AC1200雙頻', 'default_price' => 800.00],
        ['name' => 'Creative Sound Blaster Z', 'category' => 'Other', 'description' => 'PCIe音效卡，7.1聲道，遊戲音效增強', 'default_price' => 2800.00],
        ['name' => 'SATA 3.0 Cable', 'category' => 'Other', 'description' => 'SATA 3.0傳輸線，高速數據傳輸', 'default_price' => 150.00],
        ['name' => 'Thermal Paste', 'category' => 'Other', 'description' => '導熱膏，CPU散熱器專用', 'default_price' => 200.00],
        
        // 服務項目
        ['name' => '電腦組裝服務', 'category' => 'Services', 'description' => '專業電腦組裝，包含系統安裝和測試', 'default_price' => 1500.00],
        ['name' => '系統重灌', 'category' => 'Services', 'description' => 'Windows系統重新安裝，包含驅動程式和基本軟體', 'default_price' => 800.00],
        ['name' => '硬體檢測', 'category' => 'Services', 'description' => '全面硬體檢測與故障診斷，提供詳細報告', 'default_price' => 500.00],
        ['name' => '資料救援', 'category' => 'Services', 'description' => '硬碟資料救援服務，專業數據恢復', 'default_price' => 2000.00],
        ['name' => '電腦清潔保養', 'category' => 'Services', 'description' => '內部清潔、散熱膏更換、風扇維護', 'default_price' => 600.00],
        ['name' => '超頻調校', 'category' => 'Services', 'description' => 'CPU/GPU超頻調校，性能優化', 'default_price' => 1200.00],
        ['name' => '系統優化', 'category' => 'Services', 'description' => '系統性能優化，軟體調校', 'default_price' => 400.00]
    ];
    
    $db->beginTransaction();
    
    try {
        $sql = "INSERT INTO pc_parts (name, category, description, default_price, is_active, sort_order) VALUES (?, ?, ?, ?, 1, ?)";
        
        $insertedCount = 0;
        foreach ($pcParts as $index => $part) {
            $db->insert($sql, [
                $part['name'],
                $part['category'],
                $part['description'],
                $part['default_price'],
                $index + 1
            ]);
            $insertedCount++;
        }
        
        $db->commit();
        
        Response::successAndExit([
            'inserted_count' => $insertedCount,
            'total_parts' => count($pcParts)
        ], "成功初始化 {$insertedCount} 個電腦零件項目");
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log('Init PC parts error: ' . $e->getMessage());
    Response::errorAndExit('初始化電腦零件數據失敗: ' . $e->getMessage());
}
?>