// 表單驗證功能
class FormValidator {
    constructor() {
        this.debounceTimers = {};
        this.validationResults = {};
    }

    // 防抖函數
    debounce(func, delay, key) {
        clearTimeout(this.debounceTimers[key]);
        this.debounceTimers[key] = setTimeout(func, delay);
    }

    // 顯示驗證消息
    showValidationMessage(fieldId, message, isValid) {
        const field = document.getElementById(fieldId);
        const existingMessage = field.parentNode.querySelector('.validation-message');
        
        // 移除現有消息
        if (existingMessage) {
            existingMessage.remove();
        }

        // 創建新消息
        if (message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `validation-message ${isValid ? 'valid' : 'invalid'}`;
            messageDiv.textContent = message;
            field.parentNode.appendChild(messageDiv);
        }

        // 更新字段樣式
        field.classList.remove('valid', 'invalid');
        if (message) {
            field.classList.add(isValid ? 'valid' : 'invalid');
        }
    }

    // 驗證用戶名
    async validateUsername(username) {
        if (!username) {
            this.showValidationMessage('username', '', false);
            this.validationResults.username = false;
            return false;
        }

        // 客戶端格式驗證
        if (!/^[a-zA-Z0-9_]+$/.test(username)) {
            this.showValidationMessage('username', 'Username can only contain letters, numbers, and underscores', false);
            this.validationResults.username = false;
            return false;
        }

        if (username.length < 6 || username.length > 20) {
            this.showValidationMessage('username', 'Username must be between 6 and 20 characters', false);
            this.validationResults.username = false;
            return false;
        }

        try {
            const response = await fetch('php/check_username.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username: username })
            });

            const result = await response.json();
            
            if (result.error) {
                this.showValidationMessage('username', 'Validation error', false);
                this.validationResults.username = false;
                return false;
            }

            this.showValidationMessage('username', result.message, result.available);
            this.validationResults.username = result.available;
            return result.available;
        } catch (error) {
            console.error('Username validation error:', error);
            this.showValidationMessage('username', 'Validation failed', false);
            this.validationResults.username = false;
            return false;
        }
    }

    // 驗證Email
    async validateEmail(email) {
        if (!email) {
            this.showValidationMessage('email', '', false);
            this.validationResults.email = false;
            return false;
        }

        // 客戶端Email格式驗證
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            this.showValidationMessage('email', 'Invalid email format', false);
            this.validationResults.email = false;
            return false;
        }

        try {
            const response = await fetch('php/check_email.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email: email })
            });

            const result = await response.json();
            
            if (result.error) {
                this.showValidationMessage('email', 'Validation error', false);
                this.validationResults.email = false;
                return false;
            }

            this.showValidationMessage('email', result.message, result.valid && result.available);
            this.validationResults.email = result.valid && result.available;
            return result.valid && result.available;
        } catch (error) {
            console.error('Email validation error:', error);
            this.showValidationMessage('email', 'Validation failed', false);
            this.validationResults.email = false;
            return false;
        }
    }

    // 驗證姓名（不允許特殊符號）
    validateName(name, fieldId) {
        if (!name) {
            this.showValidationMessage(fieldId, '', false);
            this.validationResults[fieldId] = false;
            return false;
        }

        // 只允許字母、空格、連字符和撇號
        const nameRegex = /^[a-zA-Z\s\-']+$/;
        if (!nameRegex.test(name)) {
            this.showValidationMessage(fieldId, 'Name can only contain letters, spaces, hyphens, and apostrophes', false);
            this.validationResults[fieldId] = false;
            return false;
        }

        if (name.length < 1 || name.length > 50) {
            this.showValidationMessage(fieldId, 'Name must be between 1 and 50 characters', false);
            this.validationResults[fieldId] = false;
            return false;
        }

        this.showValidationMessage(fieldId, 'Valid name', true);
        this.validationResults[fieldId] = true;
        return true;
    }

    // 驗證電話號碼（只允許數字）
    validatePhone(phone) {
        if (!phone) {
            this.showValidationMessage('phone', '', false);
            this.validationResults.phone = false;
            return false;
        }

        // 只允許數字、空格、連字符和括號
        const phoneRegex = /^[0-9\s\-\(\)\+]+$/;
        if (!phoneRegex.test(phone)) {
            this.showValidationMessage('phone', 'Phone number can only contain numbers, spaces, hyphens, parentheses, and plus sign', false);
            this.validationResults.phone = false;
            return false;
        }

        // 移除所有非數字字符來檢查長度
        const digitsOnly = phone.replace(/\D/g, '');
        if (digitsOnly.length < 8 || digitsOnly.length > 15) {
            this.showValidationMessage('phone', 'Phone number must contain 8-15 digits', false);
            this.validationResults.phone = false;
            return false;
        }

        this.showValidationMessage('phone', 'Valid phone number', true);
        this.validationResults.phone = true;
        return true;
    }

    // 驗證密碼
    validatePassword(password) {
        if (!password) {
            this.showValidationMessage('password', '', false);
            this.validationResults.password = false;
            return false;
        }

        const errors = [];
        if (password.length < 8) errors.push('at least 8 characters');
        if (!/[A-Z]/.test(password)) errors.push('one uppercase letter');
        if (!/[a-z]/.test(password)) errors.push('one lowercase letter');
        if (!/[0-9]/.test(password)) errors.push('one number');
        if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) errors.push('one special character');

        if (errors.length > 0) {
            this.showValidationMessage('password', `Password must contain ${errors.join(', ')}`, false);
            this.validationResults.password = false;
            return false;
        }

        this.showValidationMessage('password', 'Strong password', true);
        this.validationResults.password = true;
        return true;
    }

    // 驗證確認密碼
    validateConfirmPassword(password, confirmPassword) {
        if (!confirmPassword) {
            this.showValidationMessage('confirm_password', '', false);
            this.validationResults.confirm_password = false;
            return false;
        }

        if (password !== confirmPassword) {
            this.showValidationMessage('confirm_password', 'Passwords do not match', false);
            this.validationResults.confirm_password = false;
            return false;
        }

        this.showValidationMessage('confirm_password', 'Passwords match', true);
        this.validationResults.confirm_password = true;
        return true;
    }

    // 檢查所有驗證是否通過
    isFormValid() {
        return Object.values(this.validationResults).every(result => result === true);
    }

    // 初始化表單驗證
    initializeValidation() {
        const usernameField = document.getElementById('username');
        const emailField = document.getElementById('email');
        const firstNameField = document.getElementById('first_name');
        const lastNameField = document.getElementById('last_name');
        const phoneField = document.getElementById('phone');
        const passwordField = document.getElementById('password');
        const confirmPasswordField = document.getElementById('confirm_password');
        const submitButton = document.querySelector('button[type="submit"]');

        // 用戶名驗證
        if (usernameField) {
            usernameField.addEventListener('input', (e) => {
                this.debounce(() => {
                    this.validateUsername(e.target.value);
                }, 500, 'username');
            });
        }

        // Email驗證
        if (emailField) {
            emailField.addEventListener('input', (e) => {
                this.debounce(() => {
                    this.validateEmail(e.target.value);
                }, 500, 'email');
            });
        }

        // 姓名驗證
        if (firstNameField) {
            firstNameField.addEventListener('input', (e) => {
                this.validateName(e.target.value, 'first_name');
            });
        }

        if (lastNameField) {
            lastNameField.addEventListener('input', (e) => {
                this.validateName(e.target.value, 'last_name');
            });
        }

        // 電話驗證
        if (phoneField) {
            phoneField.addEventListener('input', (e) => {
                this.validatePhone(e.target.value);
            });
        }

        // 密碼驗證
        if (passwordField) {
            passwordField.addEventListener('input', (e) => {
                this.validatePassword(e.target.value);
                // 如果確認密碼已填寫，重新驗證
                if (confirmPasswordField && confirmPasswordField.value) {
                    this.validateConfirmPassword(e.target.value, confirmPasswordField.value);
                }
            });
        }

        // 確認密碼驗證
        if (confirmPasswordField) {
            confirmPasswordField.addEventListener('input', (e) => {
                const password = passwordField ? passwordField.value : '';
                this.validateConfirmPassword(password, e.target.value);
            });
        }

        // 表單提交驗證
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', (e) => {
                if (!this.isFormValid()) {
                    e.preventDefault();
                    alert('Please fix all validation errors before submitting.');
                }
            });
        }
    }
}

// 初始化驗證器
document.addEventListener('DOMContentLoaded', () => {
    const validator = new FormValidator();
    validator.initializeValidation();
});