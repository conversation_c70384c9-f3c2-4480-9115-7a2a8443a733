<?php
/**
 * 管理收據配置
 * KMS PC Receipt Maker
 */

session_start();

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

// 檢查用戶是否已登入
if (!isset($_SESSION['user_id'])) {
    Response::errorAndExit('請先登入', 401);
}

$userId = $_SESSION['user_id'];
$method = $_SERVER['REQUEST_METHOD'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    switch ($method) {
        case 'GET':
            // 獲取用戶的配置列表
            $sql = "SELECT * FROM receipt_configurations WHERE user_id = ? ORDER BY created_at DESC";
            $configurations = $db->fetchAll($sql, [$userId]);
            
            // 解析JSON字段
            foreach ($configurations as &$config) {
                if ($config['items']) {
                    $config['items'] = json_decode($config['items'], true);
                }
                if ($config['default_items']) {
                    $config['default_items'] = json_decode($config['default_items'], true);
                }
                if ($config['settings']) {
                    $config['settings'] = json_decode($config['settings'], true);
                }
            }
            
            Response::successAndExit($configurations, '配置列表獲取成功');
            break;
            
        case 'POST':
            // 創建新配置
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || !isset($input['name'])) {
                Response::errorAndExit('缺少配置名稱', 400);
            }
            
            $name = trim($input['name']);
            $description = trim($input['description'] ?? '');
            $category = trim($input['category'] ?? 'general');
            $isDefault = isset($input['is_default']) ? (bool)$input['is_default'] : false;
            // 移除is_public功能，所有配置都是私有的
            
            // 客戶信息預設
            $customerName = trim($input['customer_name'] ?? '');
            $customerPhone = trim($input['customer_phone'] ?? '');
            $customerEmail = trim($input['customer_email'] ?? '');
            $customerAddress = trim($input['customer_address'] ?? '');
            $companyName = trim($input['company_name'] ?? '');
            $taxId = trim($input['tax_id'] ?? '');
            
            // 支付和稅務設定
            $paymentMethod = trim($input['payment_method'] ?? 'Cash');
            $currency = trim($input['currency'] ?? 'USD');
            $taxRate = floatval($input['tax_rate'] ?? 0);
            $discountRate = floatval($input['discount_rate'] ?? 0);
            
            // 項目配置
            $items = isset($input['items']) ? json_encode($input['items']) : null;
            $defaultItems = isset($input['default_items']) ? json_encode($input['default_items']) : null;
            
            // 外觀設定
            $templateStyle = trim($input['template_style'] ?? 'default');
            $colorScheme = trim($input['color_scheme'] ?? 'blue');
            $logoPath = trim($input['logo_path'] ?? '');
            $headerText = trim($input['header_text'] ?? '');
            $footerText = trim($input['footer_text'] ?? '');
            
            // 其他設定
            $notes = trim($input['notes'] ?? '');
            $settings = isset($input['settings']) ? json_encode($input['settings']) : null;
            
            // 如果設為預設，先取消其他預設配置
            if ($isDefault) {
                $updateSql = "UPDATE receipt_configurations SET is_default = 0 WHERE user_id = ?";
                $db->execute($updateSql, [$userId]);
            }
            
            $sql = "
                INSERT INTO receipt_configurations (
                    user_id, name, description, is_default, category,
                    customer_name, customer_phone, customer_email, customer_address, company_name, tax_id,
                    payment_method, currency, tax_rate, discount_rate,
                    items, default_items,
                    template_style, color_scheme, logo_path, header_text, footer_text,
                    notes, settings
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ";
            
            $configId = $db->insert($sql, [
                $userId, $name, $description, $isDefault, $category,
                $customerName, $customerPhone, $customerEmail, $customerAddress, $companyName, $taxId,
                $paymentMethod, $currency, $taxRate, $discountRate,
                $items, $defaultItems,
                $templateStyle, $colorScheme, $logoPath, $headerText, $footerText,
                $notes, $settings
            ]);
            
            Response::successAndExit(['id' => $configId], '配置創建成功');
            break;
            
        case 'PUT':
            // 更新配置
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || !isset($input['id'])) {
                Response::errorAndExit('缺少配置ID', 400);
            }
            
            $configId = intval($input['id']);
            
            // 檢查配置是否屬於當前用戶
            $checkSql = "SELECT user_id FROM receipt_configurations WHERE id = ?";
            $config = $db->fetch($checkSql, [$configId]);
            
            if (!$config || $config['user_id'] != $userId) {
                Response::errorAndExit('無權限修改此配置', 403);
            }
            
            // 更新配置（類似創建的邏輯）
            $name = trim($input['name']);
            $description = trim($input['description'] ?? '');
            $isDefault = isset($input['is_default']) ? (bool)$input['is_default'] : false;
            
            // 如果設為預設，先取消其他預設配置
            if ($isDefault) {
                $updateSql = "UPDATE receipt_configurations SET is_default = 0 WHERE user_id = ? AND id != ?";
                $db->execute($updateSql, [$userId, $configId]);
            }
            
            $sql = "UPDATE receipt_configurations SET name = ?, description = ?, is_default = ?, updated_at = NOW() WHERE id = ?";
            $db->execute($sql, [$name, $description, $isDefault, $configId]);
            
            Response::successAndExit(['id' => $configId], '配置更新成功');
            break;
            
        case 'DELETE':
            // 刪除配置
            $configId = intval($_GET['id'] ?? 0);
            
            if ($configId <= 0) {
                Response::errorAndExit('無效的配置ID', 400);
            }
            
            // 檢查配置是否屬於當前用戶
            $checkSql = "SELECT user_id FROM receipt_configurations WHERE id = ?";
            $config = $db->fetch($checkSql, [$configId]);
            
            if (!$config || $config['user_id'] != $userId) {
                Response::errorAndExit('無權限刪除此配置', 403);
            }
            
            $sql = "DELETE FROM receipt_configurations WHERE id = ?";
            $db->execute($sql, [$configId]);
            
            Response::successAndExit(['id' => $configId], '配置刪除成功');
            break;
            
        default:
            Response::errorAndExit('不支持的請求方法', 405);
    }
    
} catch (Exception $e) {
    error_log('Manage configurations error: ' . $e->getMessage());
    Response::errorAndExit('配置管理失敗: ' . $e->getMessage());
}
?>