<?php
session_start();

// 檢查用戶是否已登入
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// 獲取用戶信息
$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'];
$user_type = $_SESSION['user_type'];

// 引入點數管理器並獲取用戶點數
require_once 'CreditManager.php';
$creditManager = new CreditManager();
$user_credits = $creditManager->getUserCredits($user_id);
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="KMS Receipt Maker is a tool to easily create, manage, and print customer receipts.">
    <title data-lang="app_title">KMS Receipt Maker</title>
    
    <!-- Bootstrap CSS -->
    <link href="css/bootstrap.css" rel="stylesheet">
    <!-- Font Awesome removed - using Unicode symbols instead -->
    <!-- Custom CSS -->
    <link href="css/style.css" rel="stylesheet">
    <link href="css/receipt.css" rel="stylesheet">
    <link href="css/receipt-preview.css" rel="stylesheet">
    <link href="css/messages-modals.css" rel="stylesheet">
    <link href="css/custom-modals.css" rel="stylesheet">
    <link href="css/custom-modal.css" rel="stylesheet">
    <link href="css/dynamic-styles.css" rel="stylesheet">
    <link href="css/receipt-details-modal.css" rel="stylesheet">
    <link href="css/account-deletion.css" rel="stylesheet">
    <link href="css/company-info.css" rel="stylesheet">
</head>
<body>

    <!-- =================================================================
    RIGHT-SIDE QUICK NAVIGATION
    ================================================================== -->
    <nav class="quick-nav" aria-label="Section shortcuts">
        <!-- 聯繫方式按鈕 -->
        <div class="contact-buttons">
            <a href="#" class="contact-btn email-btn" title="Send Email" aria-label="Send Email" id="contact-email-btn">
                <span class="icon-symbol">📧</span>
                <span class="contact-text" data-lang="default_company_email"><EMAIL></span>
            </a>
            <a href="#" class="contact-btn phone-btn" title="Call Phone" aria-label="Call Phone" id="contact-phone-btn">
                <span class="icon-symbol">📞</span>
                <span class="contact-text" data-lang="default_company_phone">************</span>
            </a>
        </div>

        <a href="#anchorCreate" title="Create New Receipt" aria-label="Create New Receipt">
            <span class="icon-symbol">📄</span>
            <span class="nav-text" data-lang="create_receipt">Create New Receipt</span>
        </a>
        <a href="#anchorAddItem" title="Add Item" aria-label="Add Item">
            <span class="icon-symbol">➕</span>
            <span class="nav-text" data-lang="add_item">Add Item</span>
        </a>
        <a href="#anchorReceiptItems" title="Receipt Items" aria-label="Receipt Items">
            <span class="icon-symbol">📋</span>
            <span class="nav-text" data-lang="receipt_items">Receipt Items</span>
        </a>
        <a href="#anchorReceiptPreview" title="Receipt Preview" aria-label="Receipt Preview">
            <span class="icon-symbol">👁</span>
            <span class="nav-text" data-lang="receipt_preview">Receipt Preview</span>
        </a>

        <!-- Save and Print Receipt buttons moved here -->
        <div class="quick-nav-actions">
            <button type="button" class="quick-nav-btn btn-save" onclick="saveReceipt()">
                <span class="icon-symbol">💾</span>
                <span class="nav-text" data-lang="save_receipt">Save Receipt</span>
            </button>
            <button type="button" class="quick-nav-btn btn-print" onclick="printReceipt()">
                <span class="icon-symbol">🖨</span>
                <span class="nav-text" data-lang="print_receipt">Print Receipt</span>
            </button>
        </div>
    </nav>

    <!-- =================================================================
    MODERN NAVIGATION BAR
    ================================================================== -->
    <nav class="kms-navbar">
        <div class="kms-nav-container">
            <div class="kms-brand">
                <span class="icon-symbol kms-brand-icon">🧾</span>
                <span class="kms-brand-text" data-lang="app_title">KMS Receipt Maker</span>
            </div>
            <div class="kms-nav-menu">
                <!-- 用戶信息顯示 -->
                <div class="kms-user-info">
                    <div class="kms-user-welcome">Welcome, <?php echo htmlspecialchars($username); ?></div>
                    <div class="user-type-badge <?php echo $user_type; ?>"><?php echo $user_type === 'admin' ? 'Admin' : 'Member'; ?></div>
                    <?php if (isset($_SESSION['is_proxy_session']) && $_SESSION['is_proxy_session']): ?>
                        <div class="proxy-session-indicator">
                            <span class="icon-symbol">🔄</span>
                            <span data-lang="proxy_session">Proxy Session</span>
                            <small>(Admin: <?php echo htmlspecialchars($_SESSION['original_admin_username']); ?>)</small>
                        </div>
                    <?php endif; ?>
                    <div class="kms-user-credits" id="userCreditsDisplay">
                        <span class="icon-symbol">💰</span>
                        <span data-lang="credits">Credits</span>: <?php echo $user_credits; ?>
                    </div>
                </div>

                <!-- 第一行按鈕 -->
                <div class="kms-nav-row">
                    <button class="kms-nav-btn active" onclick="showSection('create')" data-section="create">
                        <span class="icon-symbol">➕</span>
                        <span data-lang="nav_create">Create Receipt</span>
                    </button>
                    <button class="kms-nav-btn" onclick="showSection('history')" data-section="history" data-action="history">
                        <span class="icon-symbol">📜</span>
                        <span data-lang="nav_history">History</span>
                    </button>
                    <button class="kms-nav-btn credits-history-btn" onclick="showCreditsHistoryModal()" title="查看點數使用記錄">
                        <span class="icon-symbol">📊</span>
                        <span data-lang="credits">Credits</span>
                    </button>
                    <?php if ($user_type === 'admin'): ?>
                    <button class="kms-nav-btn admin-btn" onclick="openAdminPanel()">
                        <span class="icon-symbol">⚙️</span>
                        <span data-lang="admin_panel">Admin Panel</span>
                    </button>
                    <?php endif; ?>
                    <?php if (isset($_SESSION['is_proxy_session']) && $_SESSION['is_proxy_session']): ?>
                    <button class="kms-nav-btn return-admin-btn" onclick="returnToAdmin()">
                        <span class="icon-symbol">🔙</span>
                        <span data-lang="return_to_admin">Return to Admin</span>
                    </button>
                    <?php endif; ?>
                </div>

                <!-- 第二行按鈕 -->
                <div class="kms-nav-row">
                    <button class="kms-nav-btn profile-btn" onclick="openUserProfile()">
                        <span class="icon-symbol">👤</span>
                        <span data-lang="user_profile">User Profile</span>
                    </button>
                    <button class="kms-nav-btn kms-lang-btn" onclick="changeLanguage('en')" data-lang-code="en">
                        <span class="icon-symbol">🌐</span>
                        <span>English</span>
                    </button>
                    <button class="kms-nav-btn kms-lang-btn" onclick="changeLanguage('zh')" data-lang-code="zh">
                        <span class="icon-symbol">🌐</span>
                        <span>繁體中文</span>
                    </button>
                    <button class="kms-nav-btn delete-account-btn" onclick="showAccountDeletionModal()">
                        <span class="icon-symbol">🗑️</span>
                        <span data-lang="delete_account">Delete Account</span>
                    </button>
                </div>
            </div>
            <button class="kms-mobile-toggle" onclick="toggleMobileMenu()">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </div>
    </nav>

    <!-- =================================================================
    MAIN CONTENT AREA
    ================================================================== -->
    <main class="kms-container" role="main">
        <div class="kms-main-content">

            <!-- Create Receipt Section -->
            <section id="createSection" class="section active" aria-labelledby="create-heading">
                <div class="kms-content-wrapper">
                    <!-- Receipt Form Card -->
                    <div class="card mb-1" id="anchorCreate">
                        <header class="card-header">
                            <h5 class="card-title mb-0" id="create-heading">
                                <span class="icon-symbol me-2">➕</span>
                                <span data-lang="create_receipt">創建新收據</span>
                            </h5>
                        </header>
                        <div class="card-body">
                            <form id="receiptForm">
                                <div class="row">
                                    <!-- Receipt Info -->
                                    <div class="col-md-6 mb-1">
                                        <h3 class="text-primary border-bottom pb-2 mb-3" data-lang="receipt_info">收據信息</h3>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="receiptNumber" class="form-label" data-lang="receipt_number">收據編號</label>
                                                <input type="text" class="form-control" id="receiptNumber" placeholder="自動生成">
                                            </div>
                                            <div class="col-12 mb-3">
                                                <label for="logoUpload" class="form-label" data-lang="upload_logo">上傳 Logo</label>
                                                <input type="file" class="form-control" id="logoUpload" accept="image/*" onchange="handleLogoUpload(event)">
                                                <!-- Logo Preview Area -->
                                                <div id="logoPreview" class="logo-preview mt-3">
                                                    <div class="card">
                                                        <header class="card-header">
                                                            <h3 class="mb-0" data-lang="logo_preview">Logo 預覽</h3>
                                                        </header>
                                                        <div class="card-body">
                                                            <div class="row">
                                                                <div class="col-md-6">
                                                                    <img id="logoPreviewImage" src="" alt="Logo Preview" class="img-fluid rounded">
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <h3 data-lang="logo_info">圖片資訊</h3>
                                                                    <ul class="list-unstyled">
                                                                        <li><strong data-lang="file_name">檔案名稱:</strong> <span id="logoFileName"></span></li>
                                                                        <li><strong data-lang="file_type">檔案類型:</strong> <span id="logoFileType"></span></li>
                                                                        <li><strong data-lang="file_size">檔案大小:</strong> <span id="logoFileSize"></span></li>
                                                                        <li><strong data-lang="image_dimensions">圖片尺寸:</strong> <span id="logoImageDimensions"></span></li>
                                                                    </ul>
                                                                    <button type="button" class="btn btn-danger btn-sm" onclick="removeLogo()">
                                                                        <span class="icon-symbol me-1">🗑</span>
                                                                        <span data-lang="remove_logo">移除 Logo</span>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Customer Info -->
                                    <div class="col-md-6 mb-4">
                                        <div class="customer-info-section">
                                            <h3 class="text-primary border-bottom pb-2 mb-4" data-lang="customer_info">客戶信息</h3>
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <label for="customerName" class="form-label fw-semibold" data-lang="customer_name">客戶姓名</label>
                                                <input type="text" class="form-control" id="customerName" placeholder="Name">
                                            </div>
                                            <div class="col-md-6">
                                                <label for="customerPhone" class="form-label fw-semibold" data-lang="customer_phone">聯絡電話</label>
                                                <input type="tel" class="form-control" id="customerPhone" placeholder="Cell Phone Number">
                                            </div>
                                            <div class="col-12">
                                                <label for="customerEmail" class="form-label fw-semibold" data-lang="customer_email">電子郵件</label>
                                                <input type="email" class="form-control" id="customerEmail" placeholder="E-mail">
                                            </div>
                                            <div class="col-12">
                                                <label for="customerAddress" class="form-label fw-semibold" data-lang="customer_address">地址</label>
                                                <textarea class="form-control customer-address-textarea" id="customerAddress" rows="2" placeholder="Address"></textarea>
                                            </div>
                                            <div class="col-12">
                                                <label for="notes" class="form-label fw-semibold" data-lang="notes">備註</label>
                                                <textarea class="form-control notes-textarea" id="notes" rows="2" placeholder="Notes"></textarea>
                                            </div>
                                        </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Company Info Section -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="company-info-section">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h3 class="text-success border-bottom pb-2 mb-0" data-lang="company_info">會員公司資訊</h3>
                                                <div class="company-info-actions">
                                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="loadCompanyInfo()">
                                                        <span class="icon-symbol me-1">📥</span>
                                                        <span data-lang="load_company_info">載入公司資訊</span>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="saveCompanyInfo()">
                                                        <span class="icon-symbol me-1">💾</span>
                                                        <span data-lang="save_company_info">保存公司資訊</span>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearCompanyInfo()">
                                                        <span class="icon-symbol me-1">🗑</span>
                                                        <span data-lang="clear_company_info">清空</span>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <label for="companyName" class="form-label fw-semibold" data-lang="company_name">公司名稱 *</label>
                                                    <input type="text" class="form-control" id="companyName" placeholder="Company Name" required>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="companyWebsite" class="form-label fw-semibold" data-lang="company_website">公司網址</label>
                                                    <input type="url" class="form-control" id="companyWebsite" placeholder="https://www.company.com">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="companyPhone" class="form-label fw-semibold" data-lang="company_phone">聯絡電話</label>
                                                    <input type="tel" class="form-control" id="companyPhone" placeholder="Company Phone">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="companyEmail" class="form-label fw-semibold" data-lang="company_email">公司 E-mail</label>
                                                    <input type="email" class="form-control" id="companyEmail" placeholder="<EMAIL>">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Add Item Card -->
                    <div class="card mb-1" id="anchorAddItem">
                        <header class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <span class="icon-symbol me-2">➕</span>
                                    <span data-lang="add_item">Add Item</span>
                                </h5>
                                <button type="button" class="btn btn-select-preset btn-sm" onclick="showPresetModal()">
                                    <span class="icon-symbol me-1">📋</span>
                                    <span data-lang="select_preset">Select Preset</span>
                                </button>
                            </div>
                        </header>
                        <div class="card-body text-center">
                            <p class="text-muted mb-3" data-lang="add_item_description">Click the button below to add items to the receipt</p>
                            <button type="button" class="btn btn-primary btn-lg" onclick="showAddItemModal()">
                                <span class="icon-symbol me-2">➕</span>
                                <span data-lang="add_item">Add Item</span>
                            </button>
                        </div>
                    </div>

                    <!-- Items List & Totals Card -->
                    <div class="card mb-1" id="anchorReceiptItems">
                        <header class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <span class="icon-symbol me-2">📋</span>
                                    <span data-lang="receipt_items">Receipt Items</span>
                                </h5>
                                <div class="d-flex gap-2">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="toggleCompactView()" title="Toggle Compact View">
                                            <span class="icon-symbol">⇅</span>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="toggleDragAndDrop()" title="Toggle Drag & Drop">
                                            <span class="icon-symbol">↔</span>
                                        </button>
                                    </div>
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearAllItems()">
                                        <span class="icon-symbol me-1">🗑</span>
                                        <span data-lang="clear_all">Clear All</span>
                                    </button>
                                </div>
                                <div class="d-flex gap-3">
                                    <div class="form-group mb-0">
                                        <label for="discountType" class="form-label me-2" data-lang="discount_type">Discount Type:</label>
                                        <select class="form-control form-control-sm d-inline-block" id="discountType" onchange="updateDiscountCalculation()">
                                            <option value="amount" data-lang="discount_amount">Amount</option>
                                            <option value="percentage" data-lang="discount_percentage">Percentage</option>
                                        </select>
                                    </div>
                                    <div class="form-group mb-0">
                                        <label for="discountValue" class="form-label me-2" data-lang="discount_value">Discount Value:</label>
                                        <input type="number" class="form-control form-control-sm d-inline-block discount-input" id="discountValue" min="0" step="0.01" value="0" oninput="updateDiscountCalculation()">
                                        <span id="discountUnit" class="text-muted">$</span>
                                    </div>
                                    <div class="form-group mb-0">
                                        <label for="taxRate" class="form-label me-2" data-lang="tax_rate">Tax Rate:</label>
                                        <input type="number" class="form-control form-control-sm d-inline-block tax-input" id="taxRate" min="0" max="100" step="0.1" value="0" oninput="updateTotalsDisplay()">
                                        <span class="text-muted">%</span>
                                    </div>
                                </div>
                            </div>
                        </header>
                        <div class="card-body">
                            <div id="receiptItemsList">
                                <div class="text-center text-muted py-4">
                                    <span class="icon-symbol large">🛒</span>
                                    <p data-lang="no_items">尚未添加任何項目</p>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="d-flex flex-column align-items-center gap-3">
                                    <div class="main-action-buttons">
                                        <button type="button" class="btn btn-primary" onclick="generateReceipt()">
                                            <span class="icon-symbol me-1">📄</span>
                                            <span data-lang="generate_receipt">生成收據</span>
                                        </button>
                                        <button type="button" class="btn btn-select-preset btn-outline-success" onclick="saveReceiptConfiguration()">
                                            <span class="icon-symbol me-1">💾</span>
                                            <span data-lang="save_configuration">保存配置</span>
                                        </button>
                                        <button type="button" class="btn btn-select-preset btn-outline-info" onclick="showConfigurationModal()">
                                            <span class="icon-symbol me-1">⚙</span>
                                            <span data-lang="manage_configurations">管理配置</span>
                                        </button>
                                    </div>
                                    <div id="totalsDisplay" class="text-center">
                                        <!-- Totals will be displayed here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Receipt Preview Card -->
                    <div class="card mt-3" id="anchorReceiptPreview">
                        <header class="card-header">
                            <h5 class="card-title mb-0">
                                <span class="icon-symbol me-2">👁</span>
                                <span data-lang="receipt_preview">收據預覽</span>
                            </h5>
                        </header>
                        <div class="card-body">
                            <div id="receiptPreview" class="receipt-preview">
                                <div class="text-center text-muted">
                                    <span class="icon-symbol extra-large">📄</span>
                                    <p data-lang="preview_placeholder">收據預覽將在這裡顯示</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bottom Actions -->
                    <!-- Save and Print Receipt buttons moved to quick navigation -->
                </div>
            </section>

            <!-- History Section -->
            <section id="historySection" class="section" aria-labelledby="history-heading">
                <div class="kms-content-wrapper">
                    <div class="card">
                        <header class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0" id="history-heading">
                                    <span class="icon-symbol me-2">📜</span>
                                    <span data-lang="receipt_history">收據歷史</span>
                                </h5>
                                <div class="d-flex gap-2 align-items-center flex-wrap">
                                    <input type="text" class="form-control form-control-sm search-input-width" id="searchInput" data-lang="search_receipts" placeholder="搜索收據..." onkeypress="handleSearchKeyPress(event)">
                                    <select class="form-select form-select-sm payment-filter-width" id="paymentFilter" onchange="searchReceipts()">
                                        <option value="" data-lang="all_payment_methods">所有付款方式</option>
                                        <option value="Cash">Cash</option>
                                        <option value="Venmo">Venmo</option>
                                        <option value="Zelle">Zelle</option>
                                        <option value="Square">Square</option>
                                        <option value="Stripe">Stripe</option>
                                    </select>
                                    <input type="date" class="form-control form-control-sm date-filter-width" id="dateFromFilter" onchange="searchReceipts()">
                                    <span class="text-muted">至</span>
                                    <input type="date" class="form-control form-control-sm date-filter-width" id="dateToFilter" onchange="searchReceipts()">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="searchReceipts()"><span class="icon-symbol">🔍</span></button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFilters()"><span class="icon-symbol">✖</span></button>
                                    <div class="vr"></div>
                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="selectAllReceipts()" title="Select All"><span class="icon-symbol">☑</span></button>
                                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="clearSelection()" title="Clear Selection"><span class="icon-symbol">☐</span></button>
                                </div>
                            </div>
                        </header>
                        <div class="card-body">
                            <div id="receiptHistory">
                                <!-- History will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- =================================================================
    FIXED LOGOUT BUTTON
    ================================================================== -->
    <button class="fixed-logout-btn" onclick="logout()" title="Logout" aria-label="Logout">
        <span class="icon-symbol">🚪</span>
        <span class="logout-text" data-lang="logout">Logout</span>
    </button>

    <!-- =================================================================
    MODALS
    ================================================================== -->

    <!-- Preset Selection Modal -->
    <div id="presetModal" class="kms-modal" aria-hidden="true" role="dialog" aria-modal="true" aria-labelledby="presetModalTitle">
        <div class="kms-modal-dialog" role="document">
            <header class="kms-modal-header">
                <h5 class="kms-modal-title" id="presetModalTitle" data-lang="select_preset">選擇預設項目</h5>
                <div class="toolbar-row">
                    <button type="button" class="btn-action btn-action-select" onclick="showAddPresetForm()" title="新增預設">
                        <span class="icon-symbol me-1">➕</span>
                        <span data-lang="add_preset">新增預設</span>
                    </button>
                    <button type="button" class="kms-modal-close" onclick="hidePresetModal()" aria-label="Close">×</button>
                </div>
            </header>
            <div class="kms-modal-body">
                <!-- Add Preset Form -->
                <div id="addPresetForm" class="card mb-3 add-preset-form-hidden">
                    <header class="card-header">
                        <h3 class="mb-0" data-lang="add_preset">新增預設項目</h3>
                    </header>
                    <div class="card-body">
                        <div class="form-grid">
                            <div class="col-6">
                                <label class="kms-label" for="presetItemName" data-lang="item_name">項目名稱</label>
                                <input type="text" class="kms-input" id="presetItemName" required>
                            </div>
                            <div class="col-6">
                                <label class="kms-label" for="presetItemCategory" data-lang="item_category">分類</label>
                                <select class="kms-select" id="presetItemCategory">
                                    <option value="PC Case">PC Case</option>
                                    <option value="CPU">CPU</option>
                                    <option value="CPU Cooler">CPU Cooler</option>
                                    <option value="GPU">GPU</option>
                                    <option value="RAM">RAM</option>
                                    <option value="SSD">SSD</option>
                                    <option value="Motherboard">Motherboard</option>
                                    <option value="PSU">PSU</option>
                                    <option value="MB RGB">MB RGB</option>
                                    <option value="GPU RGB">GPU RGB</option>
                                    <option value="Fan RGB">Fan RGB</option>
                                    <option value="Other">Other</option>
                                    <option value="Services">Services</option>
                                </select>
                            </div>
                            <div class="col-4">
                                <label class="kms-label" for="presetItemOriginalPrice" data-lang="original_price">原價</label>
                                <input type="number" class="kms-input" id="presetItemOriginalPrice" min="0" step="0.01">
                            </div>
                            <div class="col-4">
                                <label class="kms-label" for="presetItemSpecialPrice" data-lang="special_price">特價</label>
                                <input type="number" class="kms-input" id="presetItemSpecialPrice" min="0" step="0.01">
                            </div>
                            <div class="col-4">
                                <label class="kms-label" for="presetItemDiscountPercent" data-lang="discount_percentage">折扣百分比</label>
                                <div class="input-inline">
                                    <input type="number" class="kms-input" id="presetItemDiscountPercent" readonly>
                                    <span class="input-suffix">%</span>
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="kms-label" for="presetItemDescription" data-lang="item_description">描述</label>
                                <input type="text" class="kms-input" id="presetItemDescription">
                            </div>
                            <div class="col-12">
                                <div class="toolbar-row justify-end">
                                    <button type="button" class="btn-action btn-action-toggle" onclick="cancelAddPreset()">
                                        <span class="icon-symbol me-1">✖</span>
                                        <span data-lang="cancel">取消</span>
                                    </button>
                                    <button type="button" class="btn-action btn-action-select" onclick="savePresetItem()">
                                        <span class="icon-symbol me-1">💾</span>
                                        <span data-lang="save">保存</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="toolbar-row">
                    <div class="grow">
                        <input type="text" class="kms-input" id="presetSearch" data-lang="search_items" placeholder="搜索項目..." onkeyup="filterPresets()">
                    </div>
                    <div>
                        <select class="kms-select" id="presetCategoryFilter" onchange="filterPresets()">
                            <option value="" data-lang="all_categories">所有分類</option>
                            <option value="PC Case">PC Case</option>
                            <option value="CPU">CPU</option>
                            <option value="CPU Cooler">CPU Cooler</option>
                            <option value="GPU">GPU</option>
                            <option value="RAM">RAM</option>
                            <option value="SSD">SSD</option>
                            <option value="Motherboard">Motherboard</option>
                            <option value="PSU">PSU</option>
                            <option value="MB RGB">MB RGB</option>
                            <option value="GPU RGB">GPU RGB</option>
                            <option value="Fan RGB">Fan RGB</option>
                            <option value="Other">Other</option>
                            <option value="Services">Services</option>
                        </select>
                    </div>
                </div>
                <div id="presetList">
                    <!-- Preset items will be loaded here -->
                </div>
            </div>
            <footer class="kms-modal-footer">
                <button type="button" class="btn-action btn-action-toggle" onclick="hidePresetModal()" data-lang="close">關閉</button>
            </footer>
        </div>
    </div>

    <!-- Configuration Management Modal -->
    <div id="configurationModal" class="kms-modal" aria-hidden="true" role="dialog" aria-modal="true" aria-labelledby="configurationModalTitle">
        <div class="kms-modal-dialog" role="document">
            <header class="kms-modal-header">
                <h5 class="kms-modal-title" id="configurationModalTitle" data-lang="manage_configurations">管理配置</h5>
                <div class="toolbar-row">
                    <button type="button" class="btn-action btn-action-select" onclick="showAddConfigForm()" title="新增配置">
                        <span class="icon-symbol me-1">➕</span>
                        <span data-lang="add_configuration">新增配置</span>
                    </button>
                    <button type="button" class="kms-modal-close" onclick="hideConfigurationModal()" aria-label="Close">×</button>
                </div>
            </header>
            <div class="kms-modal-body">
                <!-- Add Configuration Form -->
                <div id="addConfigForm" class="card mb-3 add-preset-form-hidden">
                    <header class="card-header">
                        <h3 class="mb-0" data-lang="add_configuration">新增配置</h3>
                    </header>
                    <div class="card-body">
                        <div class="form-grid config-form-centered">
                            <div class="col-12 config-input-container">
                                <label class="kms-label" for="configName" data-lang="config_name">配置名稱</label>
                                <input type="text" class="kms-input config-input-wide" id="configName" required>
                            </div>
                            <div class="col-12 config-input-container">
                                <label class="kms-label" for="configDescription" data-lang="config_description">配置描述</label>
                                <textarea class="kms-input config-input-wide" id="configDescription" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="toolbar-row justify-end">
                            <button type="button" class="btn-action btn-action-toggle" onclick="cancelAddConfig()">
                                <span class="icon-symbol me-1">❌</span>
                                <span data-lang="cancel">取消</span>
                            </button>
                            <button type="button" class="btn-action btn-action-select" onclick="saveConfigItem()">
                                <span class="icon-symbol me-1">💾</span>
                                <span data-lang="save">保存</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="search-filter-row">
                    <input type="text" class="search-input" id="configSearchInput" placeholder="Search configurations..." data-lang-placeholder="search_configs">
                    <select class="filter-select" id="configCategoryFilter">
                        <option value="" data-lang="all_categories">所有分類</option>
                        <option value="receipt" data-lang="receipt_configs">收據配置</option>
                        <option value="customer" data-lang="customer_configs">客戶配置</option>
                        <option value="system" data-lang="system_configs">系統配置</option>
                    </select>
                </div>

                <!-- Configuration List -->
                <div id="configurationList">
                    <!-- Configuration items will be loaded here -->
                </div>
            </div>
            <footer class="kms-modal-footer">
                <button type="button" class="btn-action btn-action-toggle" onclick="hideConfigurationModal()" data-lang="close">關閉</button>
            </footer>
        </div>
    </div>

    <!-- Receipt Details Modal -->
    <div class="modal fade" id="receiptModal" tabindex="-1" aria-labelledby="receiptModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <header class="modal-header">
                    <h5 class="modal-title" id="receiptModalLabel" data-lang="receipt_details">收據詳情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </header>
                <div class="modal-body">
                    <div id="receiptModalContent">
                        <!-- Receipt content will be loaded here -->
                    </div>
                </div>
                <footer class="modal-footer">
                    <button type="button" class="btn btn-info" onclick="printModalReceipt()">
                        <span class="icon-symbol me-1">🖨</span>
                        <span data-lang="print_receipt">打印收據</span>
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" data-lang="close">關閉</button>
                </footer>
            </div>
        </div>
    </div>

    <!-- Duplicate Configuration Modal Removed - Using KMS Style Modal Above -->

    <!-- Add Item Modal -->
    <div class="modal fade" id="addItemModal" tabindex="-1" aria-labelledby="addItemModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content add-item-modal-content">
                <header class="modal-header add-item-modal-header">
                    <div class="modal-title-container">
                        <div class="modal-icon">
                            <span class="icon-symbol">🛍️</span>
                        </div>
                        <h5 class="modal-title" id="addItemModalLabel">
                            <span data-lang="add_item">Add New Item</span>
                        </h5>
                    </div>
                    <button type="button" class="btn-close add-item-modal-close" data-bs-dismiss="modal" aria-label="Close">
                        <span class="icon-symbol">✕</span>
                    </button>
                </header>
                <div class="modal-body add-item-modal-body">
                    <form id="addItemModalForm" class="add-item-form">
                        <!-- Basic Information Section -->
                        <div class="form-section">
                            <h6 class="section-title">
                                <span class="icon-symbol">📝</span>
                                <span data-lang="basic_info">Basic Information</span>
                            </h6>
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label for="modalItemName" class="form-label">
                                        <span class="icon-symbol">🏷️</span>
                                        <span data-lang="item_name">Item Name</span>
                                        <span class="required">*</span>
                                    </label>
                                    <input type="text" class="form-control modern-input" id="modalItemName" required placeholder="Enter item name...">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label for="modalItemCategory" class="form-label">
                                        <span class="icon-symbol">📂</span>
                                        <span data-lang="item_category">Category</span>
                                    </label>
                                    <select class="form-select modern-select" id="modalItemCategory">
                                        <option value="">Select Category</option>
                                        <option value="PC Case">🖥️ PC Case</option>
                                        <option value="CPU">🔧 CPU</option>
                                        <option value="CPU Cooler">❄️ CPU Cooler</option>
                                        <option value="GPU">🎮 GPU</option>
                                        <option value="RAM">💾 RAM</option>
                                        <option value="SSD">💿 SSD</option>
                                        <option value="Motherboard">🔌 Motherboard</option>
                                        <option value="PSU">⚡ PSU</option>
                                        <option value="MB RGB">🌈 MB RGB</option>
                                        <option value="GPU RGB">🎨 GPU RGB</option>
                                        <option value="Fan RGB">💨 Fan RGB</option>
                                        <option value="Other">📦 Other</option>
                                        <option value="Services">🛠️ Services</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label for="modalItemDescription" class="form-label">
                                        <span class="icon-symbol">📄</span>
                                        <span data-lang="item_description">Description</span>
                                    </label>
                                    <textarea class="form-control modern-textarea" id="modalItemDescription" rows="4" placeholder="Enter item description..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Pricing Section -->
                        <div class="form-section">
                            <h6 class="section-title">
                                <span class="icon-symbol">💰</span>
                                <span data-lang="pricing_info">Pricing Information</span>
                            </h6>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="modalItemQuantity" class="form-label">
                                        <span class="icon-symbol">🔢</span>
                                        <span data-lang="quantity">Quantity</span>
                                        <span class="required">*</span>
                                    </label>
                                    <input type="number" class="form-control modern-input" id="modalItemQuantity" min="1" value="1" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="modalItemOriginalPrice" class="form-label">
                                        <span class="icon-symbol">🏷️</span>
                                        <span data-lang="original_price">Original Price</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control modern-input" id="modalItemOriginalPrice" min="0" step="0.01" placeholder="0.00">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="modalItemSpecialPrice" class="form-label">
                                        <span class="icon-symbol">💸</span>
                                        <span data-lang="special_price">Special Price</span>
                                        <span class="required">*</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control modern-input" id="modalItemSpecialPrice" min="0" step="0.01" required placeholder="0.00">
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="modalItemDiscountPercent" class="form-label">
                                        <span class="icon-symbol">🏷️</span>
                                        <span data-lang="discount_percentage">Discount %</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control modern-input" id="modalItemDiscountPercent" readonly>
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="modalItemTotal" class="form-label">
                                        <span class="icon-symbol">💵</span>
                                        <span data-lang="total_price">Total Price</span>
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control modern-input" id="modalItemTotal" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">
                                        <span class="icon-symbol">👁️</span>
                                        <span data-lang="display_options">Display Options</span>
                                    </label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="modalItemHidePrice">
                                        <label class="form-check-label" for="modalItemHidePrice" data-lang="hide_price">
                                            Hide Price (Show N/A on receipt)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <footer class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <span class="icon-symbol">✕</span>
                        <span data-lang="cancel">Cancel</span>
                    </button>
                    <button type="button" class="btn btn-success" onclick="confirmAddModalItem()">
                        <span class="icon-symbol">✓</span>
                        <span data-lang="confirm_add">Add Item</span>
                    </button>
                </footer>
            </div>
        </div>
    </div>

    <!-- =================================================================
    點數使用記錄彈窗 (Credits History Modal)
    ================================================================== -->
    <div id="creditsHistoryModal" class="kms-modal" aria-hidden="true" role="dialog" aria-labelledby="creditsHistoryModalTitle">
        <div class="kms-modal-backdrop"></div>
        <div class="kms-modal-dialog">
            <div class="kms-modal-content">
                <header class="kms-modal-header">
                    <h2 id="creditsHistoryModalTitle" class="kms-modal-title">
                        <span class="icon-symbol me-2">📊</span>
                        <span data-lang="credits_history">點數使用記錄</span>
                    </h2>
                    <button type="button" class="kms-modal-close" onclick="hideCreditsHistoryModal()" aria-label="Close">×</button>
                </header>
                <div class="kms-modal-body">
                    <!-- 統計信息 -->
                    <div class="credits-stats-grid">
                        <div class="credits-stat-card">
                            <div class="stat-icon">💰</div>
                            <div class="stat-info">
                                <h4 id="currentCredits">0</h4>
                                <p data-lang="current_credits">當前點數</p>
                            </div>
                        </div>
                        <div class="credits-stat-card">
                            <div class="stat-icon">📈</div>
                            <div class="stat-info">
                                <h4 id="totalEarned">0</h4>
                                <p data-lang="total_earned">總獲得</p>
                            </div>
                        </div>
                        <div class="credits-stat-card">
                            <div class="stat-icon">📉</div>
                            <div class="stat-info">
                                <h4 id="totalSpent">0</h4>
                                <p data-lang="total_spent">總消費</p>
                            </div>
                        </div>
                    </div>

                    <!-- 篩選工具 -->
                    <div class="toolbar-row mb-3">
                        <div class="grow">
                            <select class="kms-select" id="creditsTypeFilter" onchange="filterCreditsHistory()">
                                <option value="" data-lang="all_types">所有類型</option>
                                <option value="add" data-lang="credits_added">點數增加</option>
                                <option value="deduct" data-lang="credits_deducted">點數扣除</option>
                            </select>
                        </div>
                        <div>
                            <select class="kms-select" id="creditsDateFilter" onchange="filterCreditsHistory()">
                                <option value="" data-lang="all_time">所有時間</option>
                                <option value="today" data-lang="today">今天</option>
                                <option value="week" data-lang="this_week">本週</option>
                                <option value="month" data-lang="this_month">本月</option>
                            </select>
                        </div>
                    </div>

                    <!-- 記錄列表 -->
                    <div id="creditsHistoryList" class="credits-history-list">
                        <!-- Credits history items will be loaded here -->
                    </div>

                    <!-- 載入指示器 -->
                    <div id="creditsHistoryLoading" class="loading-indicator" style="display: none;">
                        <div class="spinner"></div>
                        <p data-lang="loading">載入中...</p>
                    </div>

                    <!-- 空狀態 -->
                    <div id="creditsHistoryEmpty" class="empty-state" style="display: none;">
                        <div class="empty-icon">📝</div>
                        <h3 data-lang="no_records">暫無記錄</h3>
                        <p data-lang="no_records_desc">您還沒有任何點數使用記錄</p>
                    </div>
                </div>
                <footer class="kms-modal-footer">
                    <button type="button" class="btn-action btn-action-toggle" onclick="hideCreditsHistoryModal()" data-lang="close">關閉</button>
                </footer>
            </div>
        </div>
    </div>

    <!-- =================================================================
    JAVASCRIPTS
    ================================================================== -->
    <!-- Libraries -->
    <script src="js/bootstrap-replacement.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <!-- Custom Scripts (in loading order) -->
    <script src="js/custom-modals.js"></script>
    <script src="js/language.js"></script>
    <script src="js/utils.js"></script>

    <!-- Core modules -->
    <script src="js/core/app-initializer.js"></script>

    <!-- Feature modules -->
    <script src="js/modules/item-manager.js"></script>
    <script src="js/modules/print-manager.js"></script>
    <script src="js/modules/pdf-generator.js"></script>
    <script src="js/modules/receipt-generator.js"></script>
    <script src="js/modules/ui-manager.js"></script>

    <!-- Main coordinator -->
    <script src="js/main.js"></script>

    <!-- Additional functionalities -->
    <script src="js/receipt-delete.js"></script>
    <script src="js/preset-manager.js"></script>
    <script src="js/config-manager.js"></script>
    <script src="js/credits-history-manager.js"></script>
    <script src="js/receipt.js"></script>

    <!-- Proxy login functionality -->
    <script>
        /**
         * 返回管理員帳號
         */
        function returnToAdmin() {
            // 顯示載入狀態
            const returnBtn = document.querySelector('.return-admin-btn');
            if (returnBtn) {
                returnBtn.disabled = true;
                returnBtn.innerHTML = '<span class="icon-symbol">⏳</span><span>Returning...</span>';
            }

            const formData = new FormData();
            formData.append('action', 'return_to_admin');

            fetch('php/admin_proxy_login.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 直接跳轉，不顯示成功消息
                    window.location.href = data.data.redirect_url;
                } else {
                    if (typeof showNotification === 'function') {
                        showNotification(data.message || 'Failed to return to admin', 'error');
                    } else {
                        alert(data.message || 'Failed to return to admin');
                    }

                    // 恢復按鈕狀態
                    if (returnBtn) {
                        returnBtn.disabled = false;
                        returnBtn.innerHTML = '<span class="icon-symbol">🔙</span><span data-lang="return_to_admin">Return to Admin</span>';
                    }
                }
            })
            .catch(error => {
                console.error('Return to admin error:', error);
                if (typeof showNotification === 'function') {
                    showNotification('Network error occurred', 'error');
                } else {
                    alert('Network error occurred');
                }

                // 恢復按鈕狀態
                if (returnBtn) {
                    returnBtn.disabled = false;
                    returnBtn.innerHTML = '<span class="icon-symbol">🔙</span><span data-lang="return_to_admin">Return to Admin</span>';
                }
            });
        }
    </script>

    <!-- Account Deletion JavaScript -->
    <script src="js/account-deletion.js"></script>

    <!-- Trial System JavaScript -->
    <script src="js/trial-system.js"></script>

    <!-- Company Info Manager JavaScript -->
    <script src="js/company-info-manager.js"></script>

</body>
</html>