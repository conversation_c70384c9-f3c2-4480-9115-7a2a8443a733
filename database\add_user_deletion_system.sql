-- =====================================================
-- 添加用戶刪除系統相關表
-- KMS Receipt Maker - User Deletion System
-- =====================================================

USE `kms_receipt_maker`;

-- =====================================================
-- User deletion logs table (user_deletion_logs)
-- 記錄所有用戶刪除操作的詳細日誌
-- =====================================================

CREATE TABLE IF NOT EXISTS `user_deletion_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Log ID',
    `deleted_user_id` int(11) NOT NULL COMMENT 'Deleted user ID',
    `deleted_username` varchar(50) NOT NULL COMMENT 'Deleted username',
    `deleted_email` varchar(100) NOT NULL COMMENT 'Deleted email',
    `deleted_user_type` enum('admin','member') NOT NULL COMMENT 'Deleted user type',
    `deleted_by` int(11) DEFAULT NULL COMMENT 'User ID who performed the deletion (NULL for self-deletion)',
    `deletion_reason` varchar(50) NOT NULL COMMENT 'Deletion reason (self_deletion, admin_deletion)',
    `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP address of the person who performed deletion',
    `user_agent` text DEFAULT NULL COMMENT 'User agent string',
    `data_summary` json DEFAULT NULL COMMENT 'Summary of deleted data (receipts count, presets count, etc.)',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Deletion timestamp',
    
    PRIMARY KEY (`id`),
    KEY `idx_deleted_user_id` (`deleted_user_id`),
    KEY `idx_deleted_by` (`deleted_by`),
    KEY `idx_deletion_reason` (`deletion_reason`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_deleted_user_type` (`deleted_user_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User deletion logs table';

-- =====================================================
-- Account deletion requests table (account_deletion_requests)
-- 存儲用戶自行刪除帳號的請求（可選的審核流程）
-- =====================================================

CREATE TABLE IF NOT EXISTS `account_deletion_requests` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Request ID',
    `user_id` int(11) NOT NULL COMMENT 'User ID who requested deletion',
    `username` varchar(50) NOT NULL COMMENT 'Username',
    `email` varchar(100) NOT NULL COMMENT 'Email',
    `user_type` enum('admin','member') NOT NULL COMMENT 'User type',
    `reason` text DEFAULT NULL COMMENT 'Deletion reason provided by user',
    `status` enum('pending','approved','rejected','completed') NOT NULL DEFAULT 'pending' COMMENT 'Request status',
    `requested_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Request timestamp',
    `processed_at` timestamp NULL DEFAULT NULL COMMENT 'Processing timestamp',
    `processed_by` int(11) DEFAULT NULL COMMENT 'Admin who processed the request',
    `admin_notes` text DEFAULT NULL COMMENT 'Admin notes',
    `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP address of requester',
    `user_agent` text DEFAULT NULL COMMENT 'User agent string',
    
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_requested_at` (`requested_at`),
    KEY `idx_processed_by` (`processed_by`),
    KEY `idx_user_type` (`user_type`),
    
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`processed_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Account deletion requests table';

-- =====================================================
-- User deletion settings table (user_deletion_settings)
-- 存儲用戶刪除系統的配置設置
-- =====================================================

CREATE TABLE IF NOT EXISTS `user_deletion_settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Setting ID',
    `setting_key` varchar(100) NOT NULL COMMENT 'Setting key',
    `setting_value` text DEFAULT NULL COMMENT 'Setting value',
    `description` varchar(255) DEFAULT NULL COMMENT 'Setting description',
    `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Is active',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Created time',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated time',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_setting_key` (`setting_key`),
    KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='User deletion settings table';

-- =====================================================
-- 插入預設用戶刪除配置
-- =====================================================

INSERT INTO `user_deletion_settings` (`setting_key`, `setting_value`, `description`) VALUES
('enable_self_deletion', '1', 'Enable users to delete their own accounts'),
('require_admin_approval', '0', 'Require admin approval for account deletion'),
('deletion_confirmation_required', '1', 'Require confirmation before deletion'),
('keep_deletion_logs', '1', 'Keep deletion logs for audit purposes'),
('log_retention_days', '365', 'Number of days to keep deletion logs'),
('allow_admin_self_deletion', '1', 'Allow admins to delete their own accounts'),
('cascade_delete_data', '1', 'Delete all user data when deleting account'),
('backup_before_deletion', '0', 'Create backup before deletion (not implemented)')
ON DUPLICATE KEY UPDATE 
    `setting_value` = VALUES(`setting_value`),
    `updated_at` = CURRENT_TIMESTAMP;

-- =====================================================
-- 創建視圖：用戶刪除統計
-- =====================================================

CREATE OR REPLACE VIEW `user_deletion_stats` AS
SELECT 
    DATE(created_at) as deletion_date,
    COUNT(*) as total_deletions,
    COUNT(CASE WHEN deletion_reason = 'self_deletion' THEN 1 END) as self_deletions,
    COUNT(CASE WHEN deletion_reason = 'admin_deletion' THEN 1 END) as admin_deletions,
    COUNT(CASE WHEN deleted_user_type = 'admin' THEN 1 END) as admin_deletions_count,
    COUNT(CASE WHEN deleted_user_type = 'member' THEN 1 END) as member_deletions_count
FROM user_deletion_logs
GROUP BY DATE(created_at)
ORDER BY deletion_date DESC;

-- =====================================================
-- 創建視圖：最近的用戶刪除記錄
-- =====================================================

CREATE OR REPLACE VIEW `recent_user_deletions` AS
SELECT 
    udl.id,
    udl.deleted_username,
    udl.deleted_email,
    udl.deleted_user_type,
    udl.deletion_reason,
    CASE 
        WHEN udl.deleted_by IS NULL THEN 'Self-deletion'
        ELSE CONCAT('Deleted by: ', u.username)
    END as deleted_by_info,
    udl.ip_address,
    udl.created_at as deleted_at
FROM user_deletion_logs udl
LEFT JOIN users u ON udl.deleted_by = u.id
ORDER BY udl.created_at DESC
LIMIT 50;

-- =====================================================
-- 創建觸發器：自動清理過期的刪除日誌
-- =====================================================

DELIMITER $$

CREATE EVENT IF NOT EXISTS `cleanup_old_deletion_logs`
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
BEGIN
    DECLARE retention_days INT DEFAULT 365;
    
    -- 獲取日誌保留天數設置
    SELECT setting_value INTO retention_days 
    FROM user_deletion_settings 
    WHERE setting_key = 'log_retention_days' AND is_active = 1
    LIMIT 1;
    
    -- 刪除過期的日誌記錄
    DELETE FROM user_deletion_logs 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    -- 刪除過期的刪除請求記錄
    DELETE FROM account_deletion_requests 
    WHERE status IN ('completed', 'rejected') 
    AND processed_at < DATE_SUB(NOW(), INTERVAL retention_days DAY);
END$$

DELIMITER ;

-- 啟用事件調度器（如果尚未啟用）
SET GLOBAL event_scheduler = ON;

-- =====================================================
-- 創建索引以提高查詢性能
-- =====================================================

-- 為刪除日誌表添加複合索引
ALTER TABLE `user_deletion_logs` 
ADD INDEX IF NOT EXISTS `idx_deletion_date_type` (`created_at`, `deleted_user_type`),
ADD INDEX IF NOT EXISTS `idx_deletion_reason_date` (`deletion_reason`, `created_at`);

-- 為刪除請求表添加複合索引
ALTER TABLE `account_deletion_requests` 
ADD INDEX IF NOT EXISTS `idx_status_requested` (`status`, `requested_at`),
ADD INDEX IF NOT EXISTS `idx_user_status` (`user_id`, `status`);

COMMIT;
