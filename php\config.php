<?php
/**
 * 數據庫配置文件
 * KMS PC Receipt Maker
 */

// 數據庫配置 - cPanel 環境
// 請根據您的 cPanel 資料庫設定更新以下資訊
define('DB_HOST', 'localhost'); // 通常是 localhost，但有些 cPanel 可能不同
define('DB_NAME', 'kms_receipt_maker'); // cPanel 資料庫名稱格式通常是 username_dbname
define('DB_USER', 'root'); // cPanel 資料庫使用者名稱
define('DB_PASS', ''); // 您的資料庫密碼
define('DB_CHARSET', 'utf8mb4');

// 應用配置
define('APP_NAME', 'KMS PC Receipt Maker');
define('APP_VERSION', '1.0.0');
define('COMPANY_NAME', 'Company name : KelvinKMS');
define('COMPANY_ADDRESS', 'Company URL : KelvinKMS.com');
define('COMPANY_PHONE', 'Phone Number : ************');
define('COMPANY_EMAIL', 'E-mail : <EMAIL>');

// 稅率配置
define('TAX_RATE', 0.00); // 10% 稅率

// 錯誤報告設置 - 生產環境配置
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
ini_set('log_errors', 1);
ini_set('html_errors', 0);

// 時區設置
date_default_timezone_set('US/Pacific');

// 創建數據庫連接
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    die('Database connection failed: ' . $e->getMessage());
}
?>
