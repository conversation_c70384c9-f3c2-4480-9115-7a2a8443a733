/**
 * 試用系統 JavaScript
 * KMS Receipt Maker - Trial System Manager
 */

class TrialSystemManager {
    constructor() {
        this.trialInfo = null;
        this.isTrialUser = false;
        this.canUseFullFeatures = false;
        this.init();
    }

    /**
     * 初始化試用系統
     */
    async init() {
        await this.loadTrialInfo();
        this.setupTrialRestrictions();
        this.showTrialStatus();
    }

    /**
     * 載入用戶試用信息
     */
    async loadTrialInfo() {
        try {
            const response = await fetch('php/get_trial_info.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=get_user_trial_info'
            });

            const data = await response.json();
            
            if (data.success) {
                this.trialInfo = data.data.trial_info;
                this.isTrialUser = data.data.is_trial_user;
                this.canUseFullFeatures = data.data.can_use_full_features;
            }
        } catch (error) {
            console.error('Error loading trial info:', error);
        }
    }

    /**
     * 設置試用期限制
     */
    setupTrialRestrictions() {
        if (!this.isTrialUser || this.canUseFullFeatures) {
            return; // 不是試用用戶或可以使用完整功能
        }

        // 禁用 Save Receipt 按鈕
        this.disableButton('saveReceiptBtn', 'save-receipt-btn', 'Save Receipt is not available during trial period');
        
        // 禁用 Print Receipt 按鈕
        this.disableButton('printReceiptBtn', 'print-receipt-btn', 'Print Receipt is not available during trial period');
        
        // 禁用快速導覽中的保存和列印按鈕
        this.disableQuickNavButtons();
        
        // 添加試用期提示到按鈕
        this.addTrialTooltips();
    }

    /**
     * 禁用指定按鈕
     */
    disableButton(buttonId, buttonClass, tooltipText) {
        // 通過 ID 查找按鈕
        const buttonById = document.getElementById(buttonId);
        if (buttonById) {
            this.applyTrialRestriction(buttonById, tooltipText);
        }

        // 通過 class 查找按鈕
        const buttonsByClass = document.querySelectorAll(`.${buttonClass}`);
        buttonsByClass.forEach(button => {
            this.applyTrialRestriction(button, tooltipText);
        });

        // 通過 onclick 屬性查找按鈕
        const allButtons = document.querySelectorAll('button');
        allButtons.forEach(button => {
            const onclick = button.getAttribute('onclick');
            if (onclick && (onclick.includes('saveReceipt') || onclick.includes('printReceipt'))) {
                this.applyTrialRestriction(button, tooltipText);
            }
        });
    }

    /**
     * 應用試用期限制到按鈕
     */
    applyTrialRestriction(button, tooltipText) {
        button.disabled = true;
        button.style.opacity = '0.5';
        button.style.cursor = 'not-allowed';
        button.title = tooltipText;
        
        // 添加試用期標記
        button.classList.add('trial-restricted');
        
        // 移除原有的點擊事件
        const originalOnclick = button.onclick;
        button.onclick = (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.showTrialRestrictionModal();
            return false;
        };
        
        // 添加視覺提示
        if (!button.querySelector('.trial-badge')) {
            const badge = document.createElement('span');
            badge.className = 'trial-badge';
            badge.innerHTML = '🔒';
            badge.style.cssText = `
                position: absolute;
                top: -5px;
                right: -5px;
                background: #ff6b6b;
                color: white;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                font-size: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10;
            `;
            
            // 確保按鈕有相對定位
            if (getComputedStyle(button).position === 'static') {
                button.style.position = 'relative';
            }
            
            button.appendChild(badge);
        }
    }

    /**
     * 禁用快速導覽按鈕
     */
    disableQuickNavButtons() {
        const quickNavButtons = document.querySelectorAll('.quick-nav-btn');
        quickNavButtons.forEach(button => {
            const text = button.textContent.toLowerCase();
            if (text.includes('save') || text.includes('print') || text.includes('保存') || text.includes('列印')) {
                this.applyTrialRestriction(button, 'This feature is not available during trial period');
            }
        });
    }

    /**
     * 添加試用期提示
     */
    addTrialTooltips() {
        const restrictedElements = document.querySelectorAll('.trial-restricted');
        restrictedElements.forEach(element => {
            element.addEventListener('mouseenter', () => {
                this.showTrialTooltip(element);
            });
        });
    }

    /**
     * 顯示試用期工具提示
     */
    showTrialTooltip(element) {
        // 移除現有的工具提示
        const existingTooltip = document.querySelector('.trial-tooltip');
        if (existingTooltip) {
            existingTooltip.remove();
        }

        // 創建工具提示
        const tooltip = document.createElement('div');
        tooltip.className = 'trial-tooltip';
        tooltip.innerHTML = `
            <div class="trial-tooltip-content">
                <span class="icon-symbol">⚠️</span>
                <span>This function is not available during trial period. Please upgrade to the full version to use this feature.</span>
            </div>
        `;

        // 設置工具提示位置
        const rect = element.getBoundingClientRect();
        tooltip.style.position = 'fixed';
        tooltip.style.top = (rect.top - 40) + 'px';
        tooltip.style.left = rect.left + 'px';
        tooltip.style.zIndex = '9999';

        // 添加到頁面
        document.body.appendChild(tooltip);

        // 3秒後自動移除
        setTimeout(() => {
            if (tooltip.parentNode) {
                tooltip.remove();
            }
        }, 3000);

        // 當滑鼠離開元素時移除工具提示
        element.addEventListener('mouseleave', () => {
            if (tooltip.parentNode) {
                tooltip.remove();
            }
        }, { once: true });
    }

    /**
     * 顯示試用期狀態
     */
    showTrialStatus() {
        if (!this.isTrialUser) {
            return;
        }

        // 在導航欄添加試用期狀態指示器
        this.addTrialStatusIndicator();
        
        // 如果試用期即將到期，顯示提醒
        if (this.trialInfo && this.trialInfo.days_remaining <= 3) {
            this.showTrialExpiryWarning();
        }
    }

    /**
     * 添加試用期狀態指示器
     */
    addTrialStatusIndicator() {
        if (document.querySelector('.trial-status-indicator')) {
            return;
        }

        const indicator = document.createElement('div');
        indicator.className = 'trial-status-indicator';
        
        let statusText = 'Trial User';
        let statusClass = 'trial-active';
        
        if (this.trialInfo) {
            const daysRemaining = this.trialInfo.days_remaining;
            
            if (daysRemaining <= 0) {
                statusText = 'Trial Expired';
                statusClass = 'trial-expired';
            } else if (daysRemaining <= 3) {
                statusText = `Trial: ${daysRemaining} days left`;
                statusClass = 'trial-expiring';
            } else {
                statusText = `Trial: ${daysRemaining} days left`;
                statusClass = 'trial-active';
            }
        }

        indicator.innerHTML = `
            <div class="trial-indicator ${statusClass}">
                <span class="trial-icon">⏰</span>
                <span class="trial-text" data-lang="trial_status">${statusText}</span>
            </div>
        `;

        // 將指示器添加到頁面右下角
        document.body.appendChild(indicator);
    }

    /**
     * 顯示試用期到期警告
     */
    showTrialExpiryWarning() {
        if (document.querySelector('.trial-expiry-warning')) {
            return;
        }

        const warning = document.createElement('div');
        warning.className = 'trial-expiry-warning';
        
        let warningMessage = 'Your trial period is expiring soon!';
        if (this.trialInfo) {
            const daysRemaining = this.trialInfo.days_remaining;
            if (daysRemaining <= 0) {
                warningMessage = 'Your trial period has expired. Please contact admin to become a paid member.';
            } else {
                warningMessage = `Your trial expires in ${daysRemaining} day${daysRemaining > 1 ? 's' : ''}. Contact admin to upgrade.`;
            }
        }

        warning.innerHTML = `
            <div class="warning-content">
                <span class="warning-icon">⚠️</span>
                <span class="warning-text">${warningMessage}</span>
                <button class="warning-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        warning.style.cssText = `
            position: fixed;
            top: 60px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 9998;
            max-width: 500px;
            width: 90%;
        `;

        const warningStyle = document.createElement('style');
        warningStyle.textContent = `
            .trial-expiry-warning .warning-content {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 12px 20px;
                background: linear-gradient(135deg, #ff6b6b, #ee5a24);
                color: white;
                border-radius: 12px;
                box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3);
                font-weight: 500;
            }
            
            .warning-close {
                background: none;
                border: none;
                color: white;
                font-size: 20px;
                cursor: pointer;
                padding: 0;
                margin-left: auto;
            }
            
            .warning-close:hover {
                opacity: 0.8;
            }
        `;

        document.head.appendChild(warningStyle);
        document.body.appendChild(warning);
    }

    /**
     * 顯示試用期限制彈窗
     */
    showTrialRestrictionModal() {
        const modal = document.createElement('div');
        modal.className = 'trial-restriction-modal';
        modal.innerHTML = `
            <div class="modal-overlay" onclick="this.parentElement.remove()">
                <div class="modal-content" onclick="event.stopPropagation()">
                    <div class="modal-header">
                        <h3>🔒 Trial Limitation</h3>
                        <button class="modal-close" onclick="this.closest('.trial-restriction-modal').remove()">×</button>
                    </div>
                    <div class="modal-body">
                        <p>This feature is not available during the trial period.</p>
                        <p>To access all features including Save Receipt and Print Receipt, please contact the administrator to upgrade to a paid membership.</p>
                        ${this.trialInfo ? `<p><strong>Trial expires in: ${this.trialInfo.days_remaining} days</strong></p>` : ''}
                    </div>
                    <div class="modal-footer">
                        <button class="btn-close" onclick="this.closest('.trial-restriction-modal').remove()">Close</button>
                    </div>
                </div>
            </div>
        `;

        const modalStyle = document.createElement('style');
        modalStyle.textContent = `
            .trial-restriction-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10000;
            }
            
            .trial-restriction-modal .modal-overlay {
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.7);
                display: flex;
                justify-content: center;
                align-items: center;
            }
            
            .trial-restriction-modal .modal-content {
                background: white;
                border-radius: 12px;
                max-width: 400px;
                width: 90%;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            }
            
            .trial-restriction-modal .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px;
                border-bottom: 1px solid #eee;
            }
            
            .trial-restriction-modal .modal-body {
                padding: 20px;
                line-height: 1.6;
            }
            
            .trial-restriction-modal .modal-footer {
                padding: 20px;
                text-align: right;
                border-top: 1px solid #eee;
            }
            
            .trial-restriction-modal .btn-close {
                background: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                cursor: pointer;
            }
            
            .trial-restriction-modal .modal-close {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #999;
            }
        `;

        document.head.appendChild(modalStyle);
        document.body.appendChild(modal);
    }

    /**
     * 刷新試用信息
     */
    async refreshTrialInfo() {
        await this.loadTrialInfo();
        this.showTrialStatus();
    }
}

// 創建全局實例
let trialSystemManager;

// 當頁面載入完成後初始化試用系統
document.addEventListener('DOMContentLoaded', () => {
    trialSystemManager = new TrialSystemManager();
});

// 全局函數
window.refreshTrialInfo = () => {
    if (trialSystemManager) {
        trialSystemManager.refreshTrialInfo();
    }
};
