<?php
/**
 * 刪除單個收據
 * KMS PC Receipt Maker
 */

require_once 'ReceiptManager.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

try {
    // 只允許 POST 請求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        Response::errorAndExit('只允許 POST 請求', 405);
    }

    // 獲取 JSON 輸入
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['id'])) {
        Response::errorAndExit('缺少收據 ID', 400);
    }
    
    $receiptId = intval($input['id']);
    
    if ($receiptId <= 0) {
        Response::errorAndExit('無效的收據 ID', 400);
    }
    
    $receiptManager = new ReceiptManager();
    $result = $receiptManager->deleteReceipt($receiptId);
    
    if ($result) {
        Response::successAndExit(null, '收據刪除成功');
    } else {
        Response::errorAndExit('收據刪除失敗');
    }
    
} catch (Exception $e) {
    error_log('Delete receipt error: ' . $e->getMessage());
    Response::errorAndExit('刪除收據時發生錯誤: ' . $e->getMessage());
}
?>