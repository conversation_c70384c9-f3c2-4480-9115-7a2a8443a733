/* 表單驗證樣式 */
:root {
    --validation-success-color: var(--color-3);
    --validation-error-color: var(--color-4);
    --validation-success-border: var(--border-color-3);
    --validation-error-border: var(--border-color-4);
    --validation-success-text: var(--text-color-3);
    --validation-error-text: var(--text-color-4);
}

/* 輸入字段驗證狀態 */
.form-control.valid {
    border-color: var(--validation-success-border);
    box-shadow: 0 0 0 0.2rem rgba(47, 255, 92, 0.25);
}

.form-control.invalid {
    border-color: var(--validation-error-border);
    box-shadow: 0 0 0 0.2rem rgba(255, 49, 49, 0.25);
}

/* 驗證消息樣式 */
.validation-message {
    font-size: 0.875rem;
    margin-top: 0.25rem;
    padding: 0.25rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.validation-message.valid {
    color: var(--validation-success-text);
}

.validation-message.invalid {
    color: var(--validation-error-text);
}

.validation-message.valid::before {
    content: "✓";
    font-weight: bold;
    color: var(--validation-success-color);
}

.validation-message.invalid::before {
    content: "✗";
    font-weight: bold;
    color: var(--validation-error-color);
}

/* 表單組樣式 */
.form-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-group .form-control {
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

/* 即時驗證加載狀態 */
.form-control.validating {
    border-color: var(--color-1);
    background-image: url('data:image/svg+xml;charset=utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><circle cx="8" cy="8" r="7" fill="none" stroke="%2300c8ff" stroke-width="2" stroke-dasharray="10.99 10.99" stroke-dashoffset="21.98"><animateTransform attributeName="transform" type="rotate" values="0 8 8;360 8 8" dur="1s" repeatCount="indefinite"/></circle></svg>');
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem;
    padding-right: 2.5rem;
}

/* 密碼強度指示器 */
.password-strength {
    margin-top: 0.5rem;
    display: flex;
    gap: 0.25rem;
}

.password-strength-bar {
    height: 0.25rem;
    flex: 1;
    background-color: #e9ecef;
    border-radius: 0.125rem;
    transition: background-color 0.3s ease;
}

.password-strength-bar.weak {
    background-color: var(--validation-error-color);
}

.password-strength-bar.medium {
    background-color: var(--color-2);
}

.password-strength-bar.strong {
    background-color: var(--validation-success-color);
}

/* 表單提交按鈕狀態 */
.btn-primary:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    opacity: 0.65;
    cursor: not-allowed;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .validation-message {
        font-size: 0.8rem;
    }
    
    .form-group {
        margin-bottom: 1.25rem;
    }
}

/* 動畫效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-0.5rem);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.validation-message {
    animation: fadeIn 0.3s ease;
}

/* 工具提示樣式 */
.validation-tooltip {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    z-index: 1000;
    margin-top: 0.25rem;
    display: none;
}

.form-control:focus + .validation-tooltip {
    display: block;
}

/* 特殊字段樣式 */
.username-field {
    position: relative;
}

.username-availability {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1rem;
}

.username-availability.available {
    color: var(--validation-success-color);
}

.username-availability.unavailable {
    color: var(--validation-error-color);
}

.username-availability.checking {
    color: var(--color-1);
}