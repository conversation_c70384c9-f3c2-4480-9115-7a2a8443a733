<?php
/**
 * 打印收據
 * KMS PC Receipt Maker
 */

session_start();

require_once 'Response.php';
require_once 'CreditManager.php';

header('Content-Type: application/json; charset=utf-8');

// 檢查用戶是否已登入
if (!isset($_SESSION['user_id'])) {
    Response::errorAndExit('請先登入', 401);
}

// 只允許POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Response::errorAndExit('只允許POST請求', 405);
}

$userId = $_SESSION['user_id'];

try {
    // 獲取JSON數據
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (!$data) {
        Response::errorAndExit('無效的JSON數據');
    }

    // 檢查用戶點數
    $creditManager = new CreditManager();
    if (!$creditManager->hasEnoughCredits($userId, 1)) {
        Response::errorAndExit('點數不足，打印收據需要1點數。當前餘額: ' . $creditManager->getUserCredits($userId));
    }

    // 扣除點數
    $creditResult = $creditManager->deductCredits(
        $userId,
        1,
        '打印收據',
        'receipt_print',
        $data['receipt_id'] ?? null
    );

    // 返回成功結果
    Response::successAndExit([
        'message' => '打印授權成功',
        'credit_info' => [
            'deducted' => 1,
            'balance_before' => $creditResult['balance_before'],
            'balance_after' => $creditResult['balance_after']
        ]
    ], '打印收據點數扣除成功');

} catch (Exception $e) {
    error_log('Print receipt error: ' . $e->getMessage());
    Response::errorAndExit('打印收據失敗: ' . $e->getMessage());
}
?>