<?php
require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

class CompanyInfoManager {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * 保存或更新會員公司資訊
     */
    public function saveCompanyInfo($userId, $companyName, $companyWebsite = '', $companyPhone = '', $companyEmail = '') {
        try {
            // 檢查是否已存在公司資訊
            $existingInfo = $this->getCompanyInfo($userId);
            
            if ($existingInfo['success'] && !empty($existingInfo['data'])) {
                // 更新現有資訊
                $sql = "UPDATE user_company_info SET 
                        company_name = ?, 
                        company_website = ?, 
                        company_phone = ?, 
                        company_email = ?, 
                        updated_at = CURRENT_TIMESTAMP 
                        WHERE user_id = ?";
                $params = [$companyName, $companyWebsite, $companyPhone, $companyEmail, $userId];
            } else {
                // 插入新資訊
                $sql = "INSERT INTO user_company_info (user_id, company_name, company_website, company_phone, company_email) 
                        VALUES (?, ?, ?, ?, ?)";
                $params = [$userId, $companyName, $companyWebsite, $companyPhone, $companyEmail];
            }
            
            $result = $this->db->execute($sql, $params);
            
            if ($result) {
                return Response::success('公司資訊保存成功', [
                    'company_name' => $companyName,
                    'company_website' => $companyWebsite,
                    'company_phone' => $companyPhone,
                    'company_email' => $companyEmail
                ]);
            } else {
                return Response::error('保存公司資訊失敗');
            }
            
        } catch (Exception $e) {
            error_log("CompanyInfoManager::saveCompanyInfo Error: " . $e->getMessage());
            return Response::error('系統錯誤：' . $e->getMessage());
        }
    }
    
    /**
     * 獲取會員公司資訊
     */
    public function getCompanyInfo($userId) {
        try {
            $sql = "SELECT company_name, company_website, company_phone, company_email, 
                           created_at, updated_at 
                    FROM user_company_info 
                    WHERE user_id = ?";
            
            $result = $this->db->fetch($sql, [$userId]);
            
            if ($result) {
                return Response::success('獲取公司資訊成功', $result);
            } else {
                return Response::success('尚未設定公司資訊', []);
            }
            
        } catch (Exception $e) {
            error_log("CompanyInfoManager::getCompanyInfo Error: " . $e->getMessage());
            return Response::error('系統錯誤：' . $e->getMessage());
        }
    }
    
    /**
     * 刪除會員公司資訊
     */
    public function deleteCompanyInfo($userId) {
        try {
            $sql = "DELETE FROM user_company_info WHERE user_id = ?";
            $result = $this->db->execute($sql, [$userId]);
            
            if ($result) {
                return Response::success('公司資訊刪除成功');
            } else {
                return Response::error('刪除公司資訊失敗');
            }
            
        } catch (Exception $e) {
            error_log("CompanyInfoManager::deleteCompanyInfo Error: " . $e->getMessage());
            return Response::error('系統錯誤：' . $e->getMessage());
        }
    }
    
    /**
     * 驗證公司資訊格式
     */
    public function validateCompanyInfo($companyName, $companyWebsite = '', $companyPhone = '', $companyEmail = '') {
        $errors = [];
        
        // 公司名稱必填
        if (empty(trim($companyName))) {
            $errors[] = '公司名稱不能為空';
        } elseif (strlen($companyName) > 255) {
            $errors[] = '公司名稱不能超過255個字符';
        }
        
        // 網址格式驗證（如果有填寫）
        if (!empty($companyWebsite) && !filter_var($companyWebsite, FILTER_VALIDATE_URL)) {
            $errors[] = '網址格式不正確';
        }
        
        // 電話格式驗證（如果有填寫）
        if (!empty($companyPhone) && !preg_match('/^[\d\-\+\(\)\s]+$/', $companyPhone)) {
            $errors[] = '電話格式不正確';
        }
        
        // 電子郵件格式驗證（如果有填寫）
        if (!empty($companyEmail) && !filter_var($companyEmail, FILTER_VALIDATE_EMAIL)) {
            $errors[] = '電子郵件格式不正確';
        }
        
        return empty($errors) ? Response::success('驗證通過') : Response::error('驗證失敗', $errors);
    }
}
?>