<?php
/**
 * 獲取用戶統計信息
 * KMS PC Receipt Maker
 */

session_start();

require_once 'ReceiptManager.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

// 檢查用戶是否已登入
if (!isset($_SESSION['user_id'])) {
    Response::errorAndExit('請先登入', 401);
}

$userId = $_SESSION['user_id'];

try {
    $receiptManager = new ReceiptManager();
    
    // 獲取用戶收據統計
    $stats = $receiptManager->getUserReceiptStats($userId);
    
    // 格式化統計數據
    $formattedStats = [
        'total_receipts' => intval($stats['total_receipts'] ?? 0),
        'total_amount' => floatval($stats['total_amount'] ?? 0),
        'avg_amount' => floatval($stats['avg_amount'] ?? 0),
        'today_receipts' => intval($stats['today_receipts'] ?? 0),
        'week_receipts' => intval($stats['week_receipts'] ?? 0),
        'month_receipts' => intval($stats['month_receipts'] ?? 0)
    ];
    
    Response::successAndExit($formattedStats, '用戶統計獲取成功');
    
} catch (Exception $e) {
    error_log('Get user stats error: ' . $e->getMessage());
    Response::errorAndExit('獲取用戶統計失敗: ' . $e->getMessage());
}
?>