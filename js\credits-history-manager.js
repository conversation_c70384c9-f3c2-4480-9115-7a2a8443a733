/**
 * Credits History Manager
 * 點數使用記錄管理器
 */
class CreditsHistoryManager {
    constructor() {
        this.isLoading = false;
        this.currentFilter = {
            type: '',
            date: ''
        };
        this.historyData = [];
    }

    /**
     * 顯示點數使用記錄彈窗
     */
    async showCreditsHistoryModal() {
        const modal = document.getElementById('creditsHistoryModal');
        if (modal) {
            modal.classList.add('is-open');
            modal.removeAttribute('aria-hidden');
            document.body.classList.add('kms-no-scroll');

            // 設置焦點到第一個可聚焦元素
            setTimeout(() => {
                const firstFocusable = modal.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
                if (firstFocusable) {
                    firstFocusable.focus();
                }
            }, 100);

            // 載入數據
            await this.loadCreditsHistory();
            await this.loadCreditsStats();

            // 綁定ESC鍵關閉
            const onKeyDown = (e) => {
                if (e.key === 'Escape') {
                    this.hideCreditsHistoryModal();
                }
            };
            document.addEventListener('keydown', onKeyDown);
            modal._kmsEscHandler = onKeyDown;

            // 綁定背景點擊關閉
            const onBackdropClick = (e) => {
                if (e.target === modal || e.target.classList.contains('kms-modal-backdrop')) {
                    this.hideCreditsHistoryModal();
                }
            };
            modal.addEventListener('click', onBackdropClick);
            modal._kmsBackdropHandler = onBackdropClick;
        }
    }

    /**
     * 隱藏點數使用記錄彈窗
     */
    hideCreditsHistoryModal() {
        const modal = document.getElementById('creditsHistoryModal');
        if (modal) {
            // 先移除焦點，避免 aria-hidden 衝突
            const activeElement = document.activeElement;
            if (activeElement && modal.contains(activeElement)) {
                activeElement.blur();
            }

            modal.classList.remove('is-open');
            modal.setAttribute('aria-hidden', 'true');
            document.body.classList.remove('kms-no-scroll');

            // 移除事件監聽器
            if (modal._kmsEscHandler) {
                document.removeEventListener('keydown', modal._kmsEscHandler);
                delete modal._kmsEscHandler;
            }
            if (modal._kmsBackdropHandler) {
                modal.removeEventListener('click', modal._kmsBackdropHandler);
                delete modal._kmsBackdropHandler;
            }

            // 恢復焦點到觸發元素
            const triggerButton = document.querySelector('.credits-history-btn');
            if (triggerButton) {
                triggerButton.focus();
            }
        }
    }

    /**
     * 載入點數統計信息
     */
    async loadCreditsStats() {
        try {
            const response = await fetch('php/get_credits_stats.php');
            const data = await response.json();

            if (data.success) {
                const stats = data.data;
                document.getElementById('currentCredits').textContent = stats.current_credits || 0;
                document.getElementById('totalEarned').textContent = stats.total_earned || 0;
                document.getElementById('totalSpent').textContent = stats.total_spent || 0;
            } else {
                console.error('Failed to load credits stats:', data.message);
            }
        } catch (error) {
            console.error('Error loading credits stats:', error);
        }
    }

    /**
     * 載入點數使用記錄
     */
    async loadCreditsHistory() {
        if (this.isLoading) return;

        this.isLoading = true;
        this.showLoading();

        try {
            const params = new URLSearchParams();
            if (this.currentFilter.type) {
                params.append('type', this.currentFilter.type);
            }
            if (this.currentFilter.date) {
                params.append('date_filter', this.currentFilter.date);
            }

            const response = await fetch(`php/get_credits_history.php?${params.toString()}`);
            const data = await response.json();

            if (data.success) {
                this.historyData = data.data;
                this.renderCreditsHistory();
            } else {
                console.error('Failed to load credits history:', data.message);
                this.showEmpty();
            }
        } catch (error) {
            console.error('Error loading credits history:', error);
            this.showEmpty();
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }

    /**
     * 渲染點數使用記錄列表
     */
    renderCreditsHistory() {
        const listContainer = document.getElementById('creditsHistoryList');
        if (!listContainer) return;

        if (this.historyData.length === 0) {
            this.showEmpty();
            return;
        }

        const html = this.historyData.map(item => {
            const isPositive = parseFloat(item.amount) > 0;
            const amountClass = isPositive ? 'positive' : 'negative';
            const itemClass = isPositive ? 'add' : 'deduct';
            const amountPrefix = isPositive ? '+' : '';
            
            const date = new Date(item.created_at);
            const formattedDate = date.toLocaleString('zh-TW', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });

            return `
                <div class="credits-history-item ${itemClass}">
                    <div class="item-header">
                        <span class="item-type">${this.getTypeText(item.type)}</span>
                        <span class="item-amount ${amountClass}">${amountPrefix}${Math.abs(item.amount)}</span>
                    </div>
                    <div class="item-description">${item.description || ''}</div>
                    <div class="item-date">${formattedDate}</div>
                </div>
            `;
        }).join('');

        listContainer.innerHTML = html;
        this.hideEmpty();
    }

    /**
     * Get type text in English
     */
    getTypeText(type) {
        const typeMap = {
            'registration': 'System Initial Credits',
            'admin_add': 'Admin Add Credits',
            'admin_deduct': 'Admin Deduct Credits',
            'admin_allocation': 'Admin Allocation',
            'save_receipt': 'Save Receipt Cost',
            'print_receipt': 'Print Receipt Cost',
            'system': 'System Operation'
        };
        return typeMap[type] || type;
    }

    /**
     * 篩選點數記錄
     */
    async filterCreditsHistory() {
        const typeFilter = document.getElementById('creditsTypeFilter');
        const dateFilter = document.getElementById('creditsDateFilter');

        this.currentFilter.type = typeFilter ? typeFilter.value : '';
        this.currentFilter.date = dateFilter ? dateFilter.value : '';

        await this.loadCreditsHistory();
    }

    /**
     * 顯示載入指示器
     */
    showLoading() {
        const loading = document.getElementById('creditsHistoryLoading');
        const list = document.getElementById('creditsHistoryList');
        const empty = document.getElementById('creditsHistoryEmpty');

        if (loading) loading.style.display = 'block';
        if (list) list.style.display = 'none';
        if (empty) empty.style.display = 'none';
    }

    /**
     * 隱藏載入指示器
     */
    hideLoading() {
        const loading = document.getElementById('creditsHistoryLoading');
        const list = document.getElementById('creditsHistoryList');

        if (loading) loading.style.display = 'none';
        if (list) list.style.display = 'block';
    }

    /**
     * 顯示空狀態
     */
    showEmpty() {
        const loading = document.getElementById('creditsHistoryLoading');
        const list = document.getElementById('creditsHistoryList');
        const empty = document.getElementById('creditsHistoryEmpty');

        if (loading) loading.style.display = 'none';
        if (list) list.style.display = 'none';
        if (empty) empty.style.display = 'block';
    }

    /**
     * 隱藏空狀態
     */
    hideEmpty() {
        const empty = document.getElementById('creditsHistoryEmpty');
        if (empty) empty.style.display = 'none';
    }

    /**
     * 刷新數據
     */
    async refresh() {
        await this.loadCreditsHistory();
        await this.loadCreditsStats();
    }
}

// 創建全局實例
window.creditsHistoryManager = new CreditsHistoryManager();

// 全局函數
window.showCreditsHistoryModal = () => {
    window.creditsHistoryManager.showCreditsHistoryModal();
};

window.hideCreditsHistoryModal = () => {
    window.creditsHistoryManager.hideCreditsHistoryModal();
};

window.filterCreditsHistory = () => {
    window.creditsHistoryManager.filterCreditsHistory();
};

// 導出到全局
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CreditsHistoryManager;
}