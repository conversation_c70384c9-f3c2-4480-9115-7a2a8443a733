<?php
/**
 * 保存收據
 * KMS PC Receipt Maker
 */

session_start();

require_once 'ReceiptManager.php';
require_once 'Response.php';
require_once 'CreditManager.php';

header('Content-Type: application/json; charset=utf-8');

// 檢查用戶是否已登入
if (!isset($_SESSION['user_id'])) {
    Response::errorAndExit('請先登入', 401);
}

// 只允許POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Response::errorAndExit('只允許POST請求', 405);
}

$userId = $_SESSION['user_id'];

try {
    // 獲取JSON數據
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    // Debug logging
    error_log('Save receipt input: ' . $input);

    if (!$data) {
        Response::errorAndExit('無效的JSON數據');
    }

    // Debug logging for data structure
    error_log('Parsed data structure: ' . print_r($data, true));
    
    // 驗證必需字段 - 客戶信息允許為空，但必須存在
    if (!isset($data['customer'])) {
        Response::errorAndExit('缺少客戶信息字段');
    }

    $requiredFields = ['items', 'totals'];
    Response::validateRequired($data, $requiredFields);
    
    // 客戶姓名不再是必填項目，允許為空
    
    // 驗證項目
    if (empty($data['items']) || !is_array($data['items'])) {
        Response::errorAndExit('收據項目不能為空');
    }
    
    // 清理輸入數據
    $cleanData = Response::sanitizeInput($data);
    
    // 驗證郵箱格式（如果提供）
    if (!empty($cleanData['customer']['email']) && !Response::validateEmail($cleanData['customer']['email'])) {
        Response::errorAndExit('郵箱格式不正確');
    }
    
    // 驗證手機號格式（如果提供）
    if (!empty($cleanData['customer']['phone']) && !Response::validatePhone($cleanData['customer']['phone'])) {
        Response::errorAndExit('手機號格式不正確');
    }
    
    // 準備收據數據
    $customerName = trim($cleanData['customer']['name'] ?? '');
    // 不再自動填入 'Guest Customer'，保持空白

    $receiptData = [
        'customer_name' => $customerName ?: null, // 如果為空則設為 null
        'customer_phone' => $cleanData['customer']['phone'] ?? null,
        'customer_email' => $cleanData['customer']['email'] ?? null,
        'customer_address' => $cleanData['customer']['address'] ?? null,
        'subtotal' => floatval($cleanData['totals']['subtotal']),
        'tax_amount' => floatval($cleanData['totals']['tax']),
        'discount_amount' => floatval($cleanData['totals']['discount'] ?? 0),
        'total_amount' => floatval($cleanData['totals']['total']),
        'original_total' => floatval($cleanData['totals']['originalTotal'] ?? 0),
        'item_savings' => floatval($cleanData['totals']['itemSavings'] ?? 0),
        'total_savings' => floatval($cleanData['totals']['totalSavings'] ?? 0),
        'savings_percent' => floatval($cleanData['totals']['savingsPercent'] ?? 0),
        'payment_method' => $cleanData['paymentMethod'] ?? 'Cash',
        'notes' => $cleanData['notes'] ?? null
    ];
    
    // 準備項目數據
    $items = [];
    foreach ($cleanData['items'] as $item) {
        if (empty($item['name'])) {
            continue;
        }

        // Handle both camelCase (from JavaScript) and snake_case formats
        $unitPrice = floatval($item['unit_price'] ?? $item['unitPrice'] ?? 0);
        $totalPrice = floatval($item['total_price'] ?? $item['totalPrice'] ?? 0);
        $originalPrice = floatval($item['original_price'] ?? $item['originalPrice'] ?? $unitPrice);
        $specialPrice = isset($item['special_price']) ? floatval($item['special_price']) :
                       (isset($item['specialPrice']) ? floatval($item['specialPrice']) : null);
        $discountPercent = intval($item['discount_percent'] ?? $item['discountPercent'] ?? 0);

        // Fix hide_price boolean conversion - ensure it's always 0 or 1
        $hidePriceValue = $item['hide_price'] ?? $item['hidePrice'] ?? false;
        $hidePrice = ($hidePriceValue === true || $hidePriceValue === 'true' || $hidePriceValue === 1 || $hidePriceValue === '1') ? 1 : 0;

        $items[] = [
            'name' => $item['name'],
            'description' => $item['description'] ?? null,
            'category' => $item['category'] ?? null,
            'quantity' => intval($item['quantity']),
            'unit_price' => $unitPrice,
            'total_price' => $totalPrice,
            'original_price' => $originalPrice,
            'special_price' => $specialPrice,
            'discount_percent' => $discountPercent,
            'hide_price' => $hidePrice
        ];
    }
    
    if (empty($items)) {
        Response::errorAndExit('沒有有效的收據項目');
    }
    
    // 檢查用戶點數
    $creditManager = new CreditManager();
    if (!$creditManager->hasEnoughCredits($userId, 1)) {
        Response::errorAndExit('點數不足，保存收據需要1點數。當前餘額: ' . $creditManager->getUserCredits($userId));
    }
    
    // 保存收據（添加用戶ID）
    $receiptManager = new ReceiptManager();
    $result = $receiptManager->saveReceipt($receiptData, $items, $userId);
    
    // 扣除點數
    try {
        $creditResult = $creditManager->deductCredits(
            $userId,
            1,
            '保存收據',
            'receipt_save',
            $result['receipt_id'] ?? null
        );
        
        // 在返回結果中包含點數信息
        $result['credit_info'] = [
            'deducted' => 1,
            'balance_before' => $creditResult['balance_before'],
            'balance_after' => $creditResult['balance_after']
        ];
        
    } catch (Exception $creditError) {
        error_log('Credit deduction error: ' . $creditError->getMessage());
        // 如果點數扣除失敗，仍然返回成功，但記錄錯誤
        $result['credit_warning'] = '收據已保存，但點數扣除失敗: ' . $creditError->getMessage();
    }
    
    Response::successAndExit($result, '收據保存成功');
    
} catch (Exception $e) {
    error_log('Save receipt error: ' . $e->getMessage());
    Response::errorAndExit('保存收據失敗: ' . $e->getMessage());
}
?>
