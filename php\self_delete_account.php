<?php
/**
 * 用戶自行刪除帳號處理器
 * KMS Receipt Maker - Self Account Deletion Handler
 */

session_start();

require_once 'config.php';
require_once 'DatabaseMySQLi.php';
require_once 'Response.php';
require_once 'UserDeletionManager.php';

// 檢查用戶是否已登入
if (!isset($_SESSION['user_id'])) {
    Response::errorAndExit();
}

// 只接受POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Response::errorAndExit();
}

$action = $_POST['action'] ?? '';
$userId = $_SESSION['user_id'];

try {
    $db = new Database();
    $conn = $db->getConnection();
    $deletionManager = new UserDeletionManager();
    
    switch ($action) {
        case 'get_deletion_info':
            // 獲取用戶數據統計
            $stats = $deletionManager->getUserDataStats($userId);
            
            // 獲取用戶基本信息
            $stmt = $conn->prepare("SELECT username, email, user_type, created_at FROM users WHERE id = ?");
            $stmt->bind_param('i', $userId);
            $stmt->execute();
            $result = $stmt->get_result();
            $userInfo = $result->fetch_assoc();
            
            Response::successAndExit([
                'user_info' => $userInfo,
                'data_stats' => $stats
            ], 'User deletion info retrieved');
            break;
            
        case 'confirm_deletion':
            $password = $_POST['password'] ?? '';
            $confirmation = $_POST['confirmation'] ?? '';
            
            if (empty($password)) {
                Response::errorAndExit();
            }
            
            if ($confirmation !== 'DELETE') {
                Response::errorAndExit();
            }
            
            // 驗證密碼
            $stmt = $conn->prepare("SELECT password FROM users WHERE id = ?");
            $stmt->bind_param('i', $userId);
            $stmt->execute();
            $result = $stmt->get_result();
            $user = $result->fetch_assoc();
            
            if (!$user || !password_verify($password, $user['password'])) {
                Response::errorAndExit();
            }
            
            // 執行刪除
            $result = $deletionManager->deleteUserCompletely($userId, $userId);
            
            if ($result['success']) {
                // 清除會話
                session_destroy();
                
                Response::successAndExit([
                    'redirect_url' => 'login.php?deleted=1'
                ], 'Account deleted successfully');
            } else {
                Response::errorAndExit($result['message']);
            }
            break;
            
        case 'request_deletion':
            $reason = $_POST['reason'] ?? '';
            
            // 檢查是否已有待處理的刪除請求
            $stmt = $conn->prepare("
                SELECT id FROM account_deletion_requests 
                WHERE user_id = ? AND status = 'pending'
            ");
            $stmt->bind_param('i', $userId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                Response::errorAndExit();
            }
            
            // 獲取用戶信息
            $stmt = $conn->prepare("SELECT username, email, user_type FROM users WHERE id = ?");
            $stmt->bind_param('i', $userId);
            $stmt->execute();
            $result = $stmt->get_result();
            $userInfo = $result->fetch_assoc();
            
            // 創建刪除請求
            $stmt = $conn->prepare("
                INSERT INTO account_deletion_requests 
                (user_id, username, email, user_type, reason, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
            
            $stmt->bind_param('issssss', 
                $userId, 
                $userInfo['username'], 
                $userInfo['email'], 
                $userInfo['user_type'], 
                $reason, 
                $ipAddress, 
                $userAgent
            );
            
            if ($stmt->execute()) {
                Response::successAndExit([], 'Deletion request submitted successfully. An admin will review your request.');
            } else {
                Response::errorAndExit('Failed to submit deletion request');
            }
            break;
            
        default:
            Response::errorAndExit('Invalid action');
            break;
    }
    
} catch (Exception $e) {
    error_log("Self deletion error: " . $e->getMessage());
    Response::errorAndExit('System error occurred');
}
?>
