/* =====================================================
   管理員面板樣式 - admin.css
   ===================================================== */

/* 基礎樣式 */
:root {
  --color-1: rgb(0, 200, 255);
  --color-2: rgb(255, 200, 0);
  --color-3: rgb(0, 255, 196);
  --color-4: rgb(255, 49, 49);
  --color-5: rgb(0, 255, 234);
  --border-color-1: rgb(0, 162, 255);
  --border-color-2: rgb(255, 180, 19);
  --border-color-3: rgb(0, 255, 196);
  --border-color-4: rgb(255, 63, 63);
  --border-color-5: rgb(12, 255, 243);
  --text-color-1: rgb(0, 0, 0);
  --text-color-2: rgb(255, 255, 255);
  --text-color-3: rgb(0, 255, 196);
  --text-color-4: rgb(255, 128, 0);
  --text-color-5: rgb(255, 0, 128);
  --admin-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  --card-shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.admin-body {
  background: var(--admin-bg);
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 0;
}

/* 布局樣式 */
.admin-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.admin-navbar {
  background: rgb(255 114 240);
  box-shadow: var(--card-shadow);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 3px solid var(--color-1);
}

.admin-nav-brand {
  display: flex;
  align-items: center;
  gap: 15px;
}

.admin-icon {
  font-size: 2rem;
  background: linear-gradient(45deg, var(--color-1), var(--color-5));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.admin-nav-brand h1 {
  margin: 0;
  color: var(--text-color-1);
  font-size: 1.8rem;
  font-weight: 700;
}

.admin-nav-actions {
  display: flex;
  gap: 10px;
}

.admin-content {
  flex: 1;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* 統計卡片樣式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* 試用統計卡片橫排顯示 */
.trial-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  padding: 1.5rem 2rem;
  background: transparent;
}

.trial-stat-card {
  background: var(--color-2);
  border-radius: 12px;
  padding: 1rem;
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  border-left: 4px solid var(--color-1);
  min-height: 80px;
}

.trial-stat-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--card-shadow-hover);
}

.trial-stat-card:nth-child(2) {
  border-left-color: var(--color-2);
}

.trial-stat-card:nth-child(3) {
  border-left-color: var(--color-4);
}

.trial-stat-card:nth-child(4) {
  border-left-color: var(--color-3);
}

.stat-card {
  background: var(--color-2);
  border-radius: 15px;
  padding: 14px;
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  border-left: 4px solid var(--color-1);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--card-shadow-hover);
}

.stat-card:nth-child(2) {
  border-left-color: var(--color-2);
}

.stat-card:nth-child(3) {
  border-left-color: var(--color-3);
}

.stat-card:nth-child(4) {
  border-left-color: var(--color-4);
}

.stat-icon {
  font-size: 28px;
  opacity: 0.8;
}

.stat-info h3 {
  margin: 0;
  font-size: 32px;
  font-weight: 700;
  color: var(--text-color-1);
}

.stat-info p {
  margin: 0;
  color: #ffffff;
  font-size: 18px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 管理區塊樣式 */
.admin-section {
  background: #00b4ff;
  border-radius: 15px;
  box-shadow: var(--card-shadow);
  overflow: hidden;
  margin-bottom: 6px;
}

.section-header {
  background: linear-gradient(45deg, var(--color-1), var(--color-5));
  color: var(--text-color-2);
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

/* 表格樣式 */
.table-responsive {
  padding: 0;
}

.admin-table {
  margin: 0;
  width: 100%;
  border-collapse: collapse;
}

.admin-table th {
  background: #00b4ff;
  color: var(--text-color-1);
  font-weight: 600;
  padding: 1rem;
  text-align: left;
  border-bottom: 2px solid #e9ecef;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-table td {
  padding: 10px;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
}

.admin-table tr {
  background-color: #ffbf00;
}

.admin-table tr:hover {
  background-color: #0080ff;
}

/* 徽章樣式 */
.badge {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-admin {
  background: linear-gradient(45deg, var(--color-4), var(--color-5));
  color: var(--text-color-2);
}

.badge-member {
  background: linear-gradient(45deg, var(--color-1), var(--color-3));
  color: var(--text-color-2);
}

.badge-pending {
  background: linear-gradient(45deg, var(--color-2), #ffc107);
  color: var(--text-color-1);
}

.badge-active {
  background: #ff9d00;
  color: var(--text-color-2);
}

.badge-inactive {
  background: linear-gradient(45deg, var(--color-4), #dc3545);
  color: var(--text-color-2);
}

/* 交互樣式 */
.action-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.btn {
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
}

.btn-primary {
  background: linear-gradient(45deg, var(--color-1), var(--color-5));
  color: var(--text-color-2);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 200, 255, 0.3);
}

.btn-success {
  background: linear-gradient(45deg, var(--color-3), #28a745);
  color: var(--text-color-2);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(47, 255, 92, 0.3);
}

.btn-danger {
  background: linear-gradient(45deg, var(--color-4), #dc3545);
  color: var(--text-color-2);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 49, 49, 0.3);
}

.btn-outline-primary {
  background: #dc3545;
  color: var(--color-1);
  border: 2px solid var(--color-1);
}

.btn-outline-primary:hover {
  background: var(--color-1);
  color: var(--text-color-2);
}

.btn-outline-danger {
  background: #dc3545;
  color: var(--color-5);
  border: 2px solid var(--color-4);
}

.btn-outline-danger:hover {
  background: var(--color-4);
  color: var(--text-color-2);
}

.btn-secondary {
  background: #6c757d;
  color: var(--text-color-2);
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

/* 模態框樣式 */
.modal-content {
  border-radius: 15px;
  border: none;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
  background: linear-gradient(45deg, var(--color-1), var(--color-5));
  color: var(--text-color-2);
  border-bottom: none;
  border-radius: 15px 15px 0 0;
}

.modal-title {
  font-weight: 600;
}

.btn-close {
  filter: invert(1);
}

.form-control {
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  padding: 0.75rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--color-1);
  box-shadow: 0 0 0 0.2rem rgba(0, 200, 255, 0.25);
  outline: none;
}

.form-label {
  font-weight: 600;
  color: var(--text-color-1);
  margin-bottom: 0.5rem;
}

/* 警告消息樣式 */
.alert {
  border-radius: 12px;
  border: none;
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
}

.alert-success {
  background: linear-gradient(45deg, rgba(47, 255, 92, 0.1), rgba(40, 167, 69, 0.1));
  color: #155724;
  border-left: 4px solid var(--color-3);
}

.alert-danger {
  background: linear-gradient(45deg, rgba(255, 49, 49, 0.1), rgba(220, 53, 69, 0.1));
  color: #721c24;
  border-left: 4px solid var(--color-4);
}

/* 響應式樣式 */
@media (max-width: 768px) {
  .admin-navbar {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .admin-nav-brand h1 {
    font-size: 1.5rem;
  }

  .admin-content {
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .trial-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    padding: 1rem;
  }

  .section-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .admin-table {
    font-size: 0.8rem;
  }

  .admin-table th,
  .admin-table td {
    padding: 0.5rem;
  }

  .action-buttons {
    flex-direction: column;
  }

  .btn-sm {
    padding: 0.5rem;
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .admin-icon {
    font-size: 1.5rem;
  }

  .admin-nav-brand h1 {
    font-size: 1.3rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .trial-stats-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    padding: 0.75rem;
  }

  .trial-stat-card {
    padding: 0.75rem;
    min-height: 70px;
  }

  .stat-icon {
    font-size: 2rem;
  }

  .stat-info h3 {
    font-size: 1.5rem;
  }
}

/* 動畫效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.admin-section,
.stat-card {
  animation: fadeInUp 0.6s ease-out;
}

.stat-card:nth-child(2) {
  animation-delay: 0.1s;
}

.stat-card:nth-child(3) {
  animation-delay: 0.2s;
}

.stat-card:nth-child(4) {
  animation-delay: 0.3s;
}

/* 載入動畫 */
.loading {
  position: relative;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top: 2px solid var(--text-color-2);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 點數管理樣式 */
.credits-display {
  font-weight: 600;
  color: var(--color-1);
  font-size: 1.1rem;
}

.credits-modal .modal-content {
  border-radius: 15px;
  border: none;
  box-shadow: var(--card-shadow-hover);
}

.credits-modal .modal-header {
  background: linear-gradient(45deg, var(--color-1), var(--color-5));
  color: var(--text-color-2);
  border-radius: 15px 15px 0 0;
  border-bottom: none;
}

.credits-modal .modal-title {
  font-weight: 600;
}

.credits-modal .btn-close {
  filter: brightness(0) invert(1);
}

.credits-action-add {
  background-color: var(--color-3);
  border-color: var(--border-color-3);
  color: var(--text-color-1);
}

.credits-action-deduct {
  background-color: var(--color-4);
  border-color: var(--border-color-4);
  color: var(--text-color-2);
}

.credits-info {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 10px;
  padding: 1rem;
  margin-bottom: 1rem;
  border-left: 4px solid var(--color-1);
}

.credits-info h6 {
  color: var(--text-color-1);
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.credits-info p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
}

/* 統計卡片擴展 - 支持6個卡片的網格 */
.stats-grid {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* 點數相關的徽章樣式 */
.badge-credits {
  background: linear-gradient(45deg, var(--color-1), var(--color-5));
  color: var(--text-color-2);
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 20px;
}

.badge-credits-low {
  background: linear-gradient(45deg, var(--color-4), #ff6b6b);
  color: var(--text-color-2);
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 點數管理按鈕樣式 */
.btn-credits {
  background: linear-gradient(45deg, var(--color-1), var(--color-5));
  border: none;
  color: var(--text-color-2);
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 15px;
  transition: all 0.3s ease;
}

.btn-credits:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 200, 255, 0.3);
  color: var(--text-color-2);
}