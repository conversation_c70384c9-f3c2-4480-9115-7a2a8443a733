/**
 * 自定義彈窗類
 */
class CustomModal {
    constructor() {
        this.overlay = null;
        this.modal = null;
        this.isOpen = false;
        this.currentResolve = null;
        this.init();
    }

    init() {
        // 創建彈窗 HTML 結構
        this.createModalHTML();
        // 綁定事件
        this.bindEvents();
    }

    createModalHTML() {
        // 創建覆蓋層
        this.overlay = document.createElement('div');
        this.overlay.className = 'custom-modal-overlay';
        
        // 創建彈窗
        this.modal = document.createElement('div');
        this.modal.className = 'custom-modal';
        
        this.modal.innerHTML = `
            <div class="custom-modal-header">
                <div class="custom-modal-icon">
                    <span id="modal-icon">⚠️</span>
                </div>
                <h3 class="custom-modal-title" id="modal-title">確認操作</h3>
            </div>
            <div class="custom-modal-content">
                <p class="custom-modal-message" id="modal-message">您確定要執行此操作嗎？</p>
            </div>
            <div class="custom-modal-actions">
                <button class="custom-modal-btn custom-modal-btn-cancel" id="modal-cancel">取消</button>
                <button class="custom-modal-btn custom-modal-btn-confirm" id="modal-confirm">確認</button>
            </div>
        `;
        
        this.overlay.appendChild(this.modal);
        document.body.appendChild(this.overlay);
    }

    bindEvents() {
        // 點擊覆蓋層關閉彈窗
        this.overlay.addEventListener('click', (e) => {
            if (e.target === this.overlay) {
                this.close(false);
            }
        });

        // 取消按鈕
        document.getElementById('modal-cancel').addEventListener('click', () => {
            this.close(false);
        });

        // 確認按鈕
        document.getElementById('modal-confirm').addEventListener('click', () => {
            this.close(true);
        });

        // ESC 鍵關閉
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close(false);
            }
        });
    }

    /**
     * 顯示確認彈窗
     * @param {Object} options - 彈窗選項
     * @param {string} options.title - 標題
     * @param {string} options.message - 訊息內容
     * @param {string} options.icon - 圖標
     * @param {string} options.confirmText - 確認按鈕文字
     * @param {string} options.cancelText - 取消按鈕文字
     * @returns {Promise<boolean>} - 用戶選擇結果
     */
    confirm(options = {}) {
        return new Promise((resolve) => {
            this.currentResolve = resolve;
            
            // 設置內容
            const {
                title = '確認操作',
                message = '您確定要執行此操作嗎？',
                icon = '⚠️',
                confirmText = '確認',
                cancelText = '取消'
            } = options;

            document.getElementById('modal-title').textContent = title;
            document.getElementById('modal-message').textContent = message;
            document.getElementById('modal-icon').textContent = icon;
            document.getElementById('modal-confirm').textContent = confirmText;
            document.getElementById('modal-cancel').textContent = cancelText;

            // 顯示彈窗
            this.show();
        });
    }

    show() {
        this.isOpen = true;
        this.overlay.classList.add('show');
        document.body.style.overflow = 'hidden';
        
        // 聚焦到確認按鈕
        setTimeout(() => {
            document.getElementById('modal-confirm').focus();
        }, 300);
    }

    close(result) {
        this.isOpen = false;
        this.overlay.classList.remove('show');
        document.body.style.overflow = '';
        
        if (this.currentResolve) {
            this.currentResolve(result);
            this.currentResolve = null;
        }
    }

    /**
     * 顯示成功彈窗
     */
    success(options = {}) {
        const defaultOptions = {
            title: '操作成功',
            icon: '✅',
            confirmText: '確定',
            cancelText: ''
        };
        
        const mergedOptions = { ...defaultOptions, ...options };
        
        // 隱藏取消按鈕
        if (!mergedOptions.cancelText) {
            document.getElementById('modal-cancel').style.display = 'none';
        }
        
        return this.confirm(mergedOptions);
    }

    /**
     * 顯示錯誤彈窗
     */
    error(options = {}) {
        const defaultOptions = {
            title: '操作失敗',
            icon: '❌',
            confirmText: '確定',
            cancelText: ''
        };
        
        const mergedOptions = { ...defaultOptions, ...options };
        
        // 隱藏取消按鈕
        if (!mergedOptions.cancelText) {
            document.getElementById('modal-cancel').style.display = 'none';
        }
        
        return this.confirm(mergedOptions);
    }

    /**
     * 顯示警告彈窗
     */
    warning(options = {}) {
        const defaultOptions = {
            title: '警告',
            icon: '⚠️',
            confirmText: '我知道了',
            cancelText: ''
        };
        
        const mergedOptions = { ...defaultOptions, ...options };
        
        // 隱藏取消按鈕
        if (!mergedOptions.cancelText) {
            document.getElementById('modal-cancel').style.display = 'none';
        }
        
        return this.confirm(mergedOptions);
    }

    /**
     * 顯示信息彈窗
     */
    info(options = {}) {
        const defaultOptions = {
            title: '提示',
            icon: 'ℹ️',
            confirmText: '確定',
            cancelText: ''
        };
        
        const mergedOptions = { ...defaultOptions, ...options };
        
        // 隱藏取消按鈕
        if (!mergedOptions.cancelText) {
            document.getElementById('modal-cancel').style.display = 'none';
        }
        
        return this.confirm(mergedOptions);
    }
}

// 創建全局實例
window.customModal = new CustomModal();

// 提供便捷的全局方法
window.showConfirm = (options) => window.customModal.confirm(options);
window.showSuccess = (options) => window.customModal.success(options);
window.showError = (options) => window.customModal.error(options);
window.showWarning = (options) => window.customModal.warning(options);
window.showInfo = (options) => window.customModal.info(options);