/**
 * ===================================================================
 * Messages and Modals Styles
 * Toast messages, alerts, and modal dialogs
 * ===================================================================
 */

/* =================================== */
/* ===== Core & Variables         ===== */
/* =================================== */

:root {
    /* Brand Colors */
    --kms-primary: #00a5ff;
    --kms-primary-light: #1fb5db;
    --kms-secondary: #00a16d;
    --kms-link: #0d6efd;
    --kms-link-bg: rgba(13, 110, 253, 0.15);

    /* Text Colors */
    --kms-text-light: #ffffff;
    --kms-text-dark: #212529;
    --kms-text-muted: #6c757d;
    --kms-text-label: #495057;

    /* Background & Border Colors */
    --kms-bg-light: #f8f9fa;
    --kms-bg-widget: #f1f3f5;
    --kms-border-color: #dee2e6;
    --kms-border-light: #e9ecef;
    --kms-border-focus: #0d6efd;

    /* Semantic Colors */
    --kms-success: #0f5132;
    --kms-success-bg: #d1e7dd;
    --kms-success-border: #badbcc;
    --kms-info: #055160;
    --kms-info-bg: #d1ecf1;
    --kms-info-border: #bee5eb;
    --kms-warning: #664d03;
    --kms-warning-bg: #fff3cd;
    --kms-warning-border: #ffecb5;
    --kms-danger: #842029;
    --kms-danger-bg: #f8d7da;
    --kms-danger-border: #f5c2c7;

    /* Golden Button Colors */
    --kms-gold-start: #FFD700;
    --kms-gold-mid: #FFA500;
    --kms-gold-end: #FF8C00;
    --kms-gold-border: #DAA520;
    --kms-gold-text: #8B4513;
    --kms-gold-hover-start: #FFED4E;
    --kms-gold-hover-mid: #FFB347;
    --kms-gold-hover-end: #FF7F50;
    --kms-gold-hover-border: #B8860B;
    --kms-gold-hover-text: #654321;
}

/* Body helper to prevent background scrolling when modal is open */
body.kms-no-scroll {
    overflow: hidden;
}


/* =================================== */
/* ===== Toast Messages & Alerts  ===== */
/* =================================== */

/* Toast Messages */
.toast-message {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    animation: slideInRight 0.3s ease-out;
}

.toast-message.removing {
    animation: slideOutRight 0.3s ease-in;
}

/* Alert Variants */
.alert-success { background-color: var(--kms-success-bg); border-color: var(--kms-success-border); color: var(--kms-success); }
.alert-info { background-color: var(--kms-info-bg); border-color: var(--kms-info-border); color: var(--kms-info); }
.alert-warning { background-color: var(--kms-warning-bg); border-color: var(--kms-warning-border); color: var(--kms-warning); }
.alert-danger { background-color: var(--kms-danger-bg); border-color: var(--kms-danger-border); color: var(--kms-danger); }


/* =================================== */
/* ===== Modal: Base Styles       ===== */
/* =================================== */

/* --- Combined styles for .kms-modal and #presetModal --- */
.kms-modal,
#presetModal.kms-modal {
    position: fixed;
    inset: 0;
    display: none;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.45);
    z-index: 1050;
}

.kms-modal.is-open,
#presetModal.kms-modal.is-open {
    display: flex;
}

.kms-modal .kms-modal-dialog,
#presetModal .kms-modal-dialog {
    width: min(1200px, 95vw);
    max-height: 90vh;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.35);
    background: var(--kms-text-light);
    display: flex;
    flex-direction: column;
}

.kms-modal .kms-modal-header,
#presetModal .kms-modal-header {
    background: var(--kms-primary);
    color: var(--kms-text-light);
    padding: 6px 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: none;
}

.kms-modal .kms-modal-title,
#presetModal .kms-modal-title {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
}

.kms-modal .kms-modal-close,
#presetModal .kms-modal-close {
    appearance: none;
    border: 0;
    background: transparent;
    color: var(--kms-text-light);
    font-size: 1.25rem;
    line-height: 1;
    padding: 0.25rem;
    border-radius: 6px;
    cursor: pointer;
    filter: invert(1);
}

.kms-modal .kms-modal-close:hover,
#presetModal .kms-modal-close:hover {
    background: rgba(255, 255, 255, 0.15);
}

.kms-modal .kms-modal-body,
#presetModal .kms-modal-body {
    padding: 1rem;
    overflow: auto;
    background-color: var(--kms-primary-light);
}

.kms-modal .kms-modal-footer,
#presetModal .kms-modal-footer {
    background-color: var(--kms-primary-light);
    border-top: 1px solid var(--kms-border-color);
    padding: 6px 12px;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}


/* =================================== */
/* ===== Preset Modal Specifics   ===== */
/* =================================== */

/* Form Controls */
#presetModal .form-grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 0.75rem 1rem;
}

#presetModal .col-12 { grid-column: span 12; }
#presetModal .col-6 { grid-column: span 6; }
#presetModal .col-4 { grid-column: span 4; }

#presetModal .kms-label {
    display: block;
    font-weight: 600;
    color: var(--kms-text-label);
    margin-bottom: 0.35rem;
}

#presetModal .kms-input,
#presetModal .kms-select,
#presetModal .kms-textarea {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid var(--kms-text-light);
    border-radius: 10px;
    background: var(--kms-secondary);
    color: var(--kms-text-light);
    margin-bottom: 3px;
}

#presetModal .kms-input:focus,
#presetModal .kms-select:focus,
#presetModal .kms-textarea:focus {
    outline: none;
    border-color: var(--kms-border-focus);
    box-shadow: 0 0 0 0.2rem var(--kms-link-bg);
}

#presetModal .toolbar-row {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

#presetModal .toolbar-row .grow {
    flex: 1 1 auto;
}

/* Preset List */
#presetModal #presetList {
    min-height: 400px;
    max-height: 60vh;
    overflow-y: auto;
    padding: 0.25rem;
}

#presetModal .preset-item {
    display: grid;
    grid-template-columns: 40px 1fr auto;
    gap: 0.75rem;
    align-items: center;
    border: 1px solid var(--kms-border-light);
    border-radius: 12px;
    padding: 3px 10px;
    background: #ffcf6b;
    margin-bottom: 2px;
    height: auto;
}

#presetModal .drag-handle {
    cursor: grab;
    color: #adb5bd;
}

#presetModal .drag-handle:active {
    cursor: grabbing;
}

#presetModal .preset-item-main {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

#presetModal .preset-title-line {
    display: flex;
    flex-wrap: wrap;
    align-items: baseline;
    gap: 0.4rem 0.6rem;
}

#presetModal .preset-name {
    font-weight: 600;
    color: var(--kms-text-dark);
}

#presetModal .preset-category {
    color: var(--kms-link);
}

#presetModal .preset-desc {
    color: var(--kms-text-muted);
}

#presetModal .preset-price-line {
    display: flex;
    align-items: center;
    gap: 0.6rem;
}

#presetModal .price-original {
    color: #00b6ff;
    text-decoration: line-through;
    font-size: 20px;
}

#presetModal .price-special,
#presetModal .price-default {
    display: inline-block;
    color: var(--kms-text-light);
    padding: 3px 8px;
    border-radius: 999px;
    font-weight: 600;
}
#presetModal .price-special { background: #ff8d00; }
#presetModal .price-default { background: #ffbc00; }

#presetModal .price-discount {
    color: #ff0000;
    font-size: 20px;
    font-weight: 600;
}

#presetModal .preset-item-actions {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
}

.preset-empty {
    text-align: center;
    color: var(--kms-text-muted);
    padding: 1rem;
}

/* =================================== */
/* ===== Configuration Modal      ===== */
/* =================================== */

#configurationModal.modal {
    position: fixed;
    inset: 0;
    display: none;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.45);
    z-index: 1050;
}

#configurationModal.modal.show {
    display: flex;
}

#configurationModal .modal-dialog {
    width: min(1800px, 95vw);
    max-height: 90vh;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.35);
    background: var(--kms-text-light);
    display: flex;
    flex-direction: column;
    margin: 0;
}

#configurationModal .modal-content {
    border: none;
    border-radius: 12px;
    background: var(--kms-text-light);
    display: flex;
    flex-direction: column;
    height: 100%;
}

#configurationModal .modal-header {
    background: var(--kms-primary);
    color: var(--kms-text-light);
    padding: 6px 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: none;
    border-radius: 12px 12px 0 0;
}

#configurationModal .modal-title {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
    color: var(--kms-text-light);
}

#configurationModal .btn-close {
    appearance: none;
    border: 0;
    background: transparent;
    color: var(--kms-text-light);
    font-size: 1.25rem;
    line-height: 1;
    padding: 0.25rem;
    border-radius: 6px;
    cursor: pointer;
    filter: invert(1);
}

#configurationModal .btn-close:hover {
    background: rgba(255, 255, 255, 0.15);
}

#configurationModal .modal-body {
    padding: 1.5rem;
    overflow: auto;
    background-color: var(--kms-primary-light);
    flex: 1;
}

#configurationModal .modal-footer {
    background-color: var(--kms-primary-light);
    border-top: 1px solid var(--kms-border-color);
    padding: 9px 18px;
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    border-radius: 0 0 12px 12px;
}

.configuration-list-container {
    min-height: 600px;
    max-height: 75vh;
    overflow-y: auto;
    padding: 0.375rem;
}

.configuration-item {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 1.125rem;
    align-items: center;
    border: 1px solid var(--kms-border-light);
    border-radius: 12px;
    padding: 4.5px 15px;
    background: #ffcf6b;
    margin-bottom: 3px;
    height: auto;
}

.configuration-item-main {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
}

.configuration-title-line {
    display: flex;
    flex-wrap: wrap;
    align-items: baseline;
    gap: 0.6rem 0.9rem;
}

.configuration-name {
    font-weight: 600;
    color: var(--kms-text-dark);
    font-size: 1.125rem;
}

.configuration-date {
    color: var(--kms-link);
    font-size: 0.9rem;
}

.configuration-desc {
    color: var(--kms-text-muted);
    font-size: 0.9rem;
}

.configuration-item-actions {
    display: inline-flex;
    align-items: center;
    gap: 0.6rem;
}

.configuration-empty {
    text-align: center;
    color: var(--kms-text-muted);
    padding: 1.5rem;
    font-size: 1.125rem;
}

/* =================================== */
/* ===== Modal: Other Content     ===== */
/* =================================== */

.logo-preview {
    border: 1px solid var(--kms-border-color);
    border-radius: 8px;
    background: var(--kms-bg-light);
}
#logoPreviewImage {
    max-height: 200px;
    object-fit: contain;
    border: 1px solid var(--kms-border-color);
    border-radius: 4px;
}
#logoPreview { display: none; }

.config-section {
    margin-bottom: 2rem;
    padding: 1rem;
    border: 1px solid var(--kms-border-color);
    border-radius: 8px;
    background: var(--kms-bg-light);
}
.config-section h6 {
    color: var(--kms-link);
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--kms-link);
}

/* =================================================================
   ADD ITEM MODAL REDESIGN
   ================================================================= */

.add-item-modal-content {
    border: none;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 255, 234, 0.2);
    background: linear-gradient(145deg, #1a1a2e 0%, #16213e 100%);
    overflow: hidden;
    color: white;
}

.add-item-modal-header {
    background: linear-gradient(135deg, var(--color-1) 0%, var(--color-2) 100%);
    border: none;
    position: relative;
    overflow: hidden;
}

.add-item-modal-header::before {
    content: '';
    position: absolute;
    inset: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 255, 234, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.modal-title-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 1;
}

.modal-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.add-item-modal-header .modal-title {
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.add-item-modal-close {
    position: absolute;
    top: 1.5rem;
    right: 2rem;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 12px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    z-index: 2;
}

.add-item-modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.add-item-modal-body {
    background: linear-gradient(145deg, #1a1a2e 0%, #16213e 100%);
}

.form-section {
    margin-bottom: 4px;
    padding: 12px;
    background: linear-gradient(145deg, #0f3460 0%, #0e4b99 100%);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 234, 0.2);
}

.section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16pxrem; /* Corrected from 16pxrem */
    font-size: 16px;
    font-weight: 600;
    color: var(--color-1);
    padding-bottom: 0.75rem;
    border-bottom: 2px solid rgba(0, 255, 234, 0.3);
}

.section-title .icon-symbol { font-size: 1.2rem; }

.add-item-form .form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--text-color-2);
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.add-item-form .form-label .icon-symbol {
    font-size: 1rem;
    color: var(--color-1);
}

.required {
    color: var(--color-4);
    font-weight: 700;
}

.modern-input,
.modern-select,
.modern-textarea {
    border: 2px solid rgba(0, 255, 234, 0.3);
    border-radius: 12px;
    padding: 3px 6px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.modern-input::placeholder,
.modern-textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.modern-input:focus,
.modern-select:focus,
.modern-textarea:focus {
    outline: none;
    border-color: var(--color-1);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(0, 255, 234, 0.2), 0 8px 24px rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
}

.modern-select {
    background: #00c4ff !important;
    color: var(--text-color-2) !important;
}

.modern-select option {
    background: var(--color-5);
    color: var(--text-color-2);
    padding: 0.5rem;
}

.modern-textarea {
    resize: vertical;
    min-height: 120px;
    min-width: 575px;
}

.input-group .input-group-text {
    background: rgba(0, 255, 234, 0.2);
    border: 2px solid rgba(0, 255, 234, 0.3);
    border-right: none;
    border-radius: 12px 0 0 12px;
    font-weight: 600;
    color: var(--color-1);
}

.input-group .modern-input {
    border-left: none;
    border-radius: 0 12px 12px 0;
}

.input-group:focus-within .input-group-text {
    border-color: var(--color-1);
    background: rgba(0, 255, 234, 0.3);
    color: white;
}

.form-check {
    padding: 1rem;
    background: rgba(0, 136, 255, 0.1);
    border-radius: 12px;
    border: 2px solid rgba(0, 136, 255, 0.3);
    transition: all 0.3s ease;
}

.form-check:hover {
    background: rgba(0, 136, 255, 0.2);
    border-color: var(--color-2);
}

.form-check-input:checked {
    background-color: var(--color-2);
    border-color: var(--color-2);
}

.form-check-label {
    font-weight: 500;
    color: var(--text-color-2);
    cursor: pointer;
}

.add-item-modal-content .modal-footer {
    background: linear-gradient(145deg, #0f3460 0%, #0e4b99 100%);
    border: none;
    border-top: 1px solid rgba(0, 255, 234, 0.2);
    gap: 1rem;
    justify-content: flex-end;
}

.add-item-modal-content .modal-footer .btn {
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.add-item-modal-content .modal-footer .btn-secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.add-item-modal-content .modal-footer .btn-secondary:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(107, 114, 128, 0.4);
}

.add-item-modal-content .modal-footer .btn-success {
    background: linear-gradient(135deg, var(--color-3) 0%, #e6b800 100%);
    color: #1a1a2e;
    border: 1px solid rgba(255, 213, 0, 0.3);
}

.add-item-modal-content .modal-footer .btn-success:hover {
    background: linear-gradient(135deg, #e6b800 0%, #cc9900 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(255, 213, 0, 0.4);
}

/* Modal States & Icons */
.modal-loading { display: flex; justify-content: center; align-items: center; padding: 3rem; }
.modal-loading .spinner-border { width: 3rem; height: 3rem; }
.modal-error { text-align: center; padding: 2rem; color: var(--kms-danger); }
.modal-error i { font-size: 3rem; margin-bottom: 1rem; }

.confirmation-dialog .modal-body { text-align: center; padding: 2rem; }
.confirmation-dialog .modal-body i { font-size: 3rem; color: #ffc107; margin-bottom: 1rem; }

.modal-icon-success { color: var(--kms-success); font-size: 1.25rem; }
.modal-icon-error   { color: var(--kms-danger); font-size: 1.25rem; }
.modal-icon-warning { color: #ffc107; font-size: 1.25rem; }
.modal-icon-info    { color: #0dcaf0; font-size: 1.25rem; }


/* =================================== */
/* ===== Reusable Buttons         ===== */
/* =================================== */

.btn-select-preset {
    background: linear-gradient(135deg, var(--kms-gold-start) 0%, var(--kms-gold-mid) 50%, var(--kms-gold-end) 100%);
    border: 2px solid var(--kms-gold-border);
    color: var(--kms-gold-text);
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.4);
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 0.5rem 1rem;
}
.btn-select-preset:hover {
    background: linear-gradient(135deg, var(--kms-gold-hover-start) 0%, var(--kms-gold-hover-mid) 50%, var(--kms-gold-hover-end) 100%);
    border-color: var(--kms-gold-hover-border);
    color: var(--kms-gold-hover-text);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(255, 215, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.5);
}
.btn-select-preset:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}
.btn-select-preset i { margin-right: 0.5rem; }

.btn-action {
    --btn-bg: var(--kms-bg-widget);
    --btn-color: var(--kms-text-dark);
    --btn-border: var(--kms-border-light);
    background: var(--btn-bg);
    color: var(--btn-color);
    border: 2px solid var(--btn-border);
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.2;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    border-radius: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
    min-height: 36px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.btn-action:hover {
    filter: brightness(0.95);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}
.btn-action .icon-symbol { font-size: 1em; }
.btn-action .btn-text { display: none; }

.btn-action-select { --btn-bg: #e8f7e9; --btn-color: #198754; --btn-border: #198754; }
.btn-action-edit   { --btn-bg: #e7f1ff; --btn-color: #0d6efd; --btn-border: #0d6efd; }
.btn-action-delete { --btn-bg: #fbe9eb; --btn-color: #dc3545; --btn-border: #dc3545; }
.btn-action-toggle { --btn-bg: #fff3cd; --btn-color: #856404; --btn-border: #ffc107; }

.btn-action-edit:hover   { --btn-bg: #cfe2ff; --btn-border: #0a58ca; }
.btn-action-delete:hover { --btn-bg: #f5c2c7; --btn-border: #b02a37; }
.btn-action-toggle:hover { --btn-bg: #ffecb5; --btn-border: #ffca2c; }


/* =================================== */
/* ===== Quick Nav & Z-index      ===== */
/* =================================== */

.quick-nav {
    position: fixed;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    z-index: 999;
}
.quick-nav a {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    padding: 10px 16px;
    margin-bottom: 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}
.quick-nav a:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateX(-5px);
}

.quicknav-safe { padding-right: 0; }
.navbar .dropdown-menu { z-index: 9999 !important; }
.navbar .nav-item.dropdown { position: relative; z-index: 9998; }


/* =================================== */
/* ===== Animations               ===== */
/* =================================== */

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(100%); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes slideOutRight {
    from { opacity: 1; transform: translateX(0); }
    to { opacity: 0; transform: translateX(100%); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}


/* =================================== */
/* ===== Credits History Modal    ===== */
/* =================================== */
#creditsHistoryModal .kms-modal-dialog {
    width: min(900px, 95vw);
}

.credits-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.credits-stat-card {
    background: var(--kms-text-light);
    border: 1px solid var(--kms-border-color);
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.credits-stat-card .stat-icon { font-size: 2rem; opacity: 0.8; }
.credits-stat-card .stat-info h4 { margin: 0; font-size: 1.5rem; font-weight: 600; color: var(--kms-primary); }
.credits-stat-card .stat-info p { margin: 0; font-size: 0.875rem; color: var(--kms-text-muted); }

.credits-history-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--kms-border-color);
    border-radius: 8px;
    background: var(--kms-text-light);
}

.credits-history-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--kms-border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.credits-history-item:last-child { border-bottom: none; }
.credits-history-item:hover { background-color: var(--kms-primary-light); }

.credits-item-info { flex: 1; }
.credits-item-title { font-weight: 600; margin: 0 0 0.25rem 0; color: var(--kms-text-dark); }
.credits-item-desc { font-size: 0.875rem; color: var(--kms-text-muted); margin: 0; }
.credits-item-amount { font-weight: 600; font-size: 1.1rem; }
.credits-item-amount.positive { color: var(--kms-success); }
.credits-item-amount.negative { color: var(--kms-danger); }
.credits-item-date { font-size: 0.75rem; color: var(--kms-text-muted); text-align: right; margin-left: 1rem; }

.loading-indicator { text-align: center; padding: 2rem; }
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--kms-border-color);
    border-top: 4px solid var(--kms-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.empty-state { text-align: center; padding: 3rem 1rem; color: var(--kms-text-muted); }
.empty-state .empty-icon { font-size: 3rem; margin-bottom: 1rem; opacity: 0.5; }
.empty-state h3 { margin: 0 0 0.5rem 0; color: var(--kms-text-muted); }
.empty-state p { margin: 0; font-size: 0.875rem; }


/* =================================== */
/* ===== Responsive Adjustments   ===== */
/* =================================== */

@media (min-width: 576px) {
    .btn-action .btn-text {
        display: inline;
        margin-left: 0.15rem;
    }
}

@media (min-width: 992px) {
    .quicknav-safe { padding-right: 60px; }
}

@media (max-width: 991px) {
    .quick-nav { display: none; }
}

@media (max-width: 768px) {
    /* Add Item Modal */
    .add-item-modal-header { padding: 1.5rem 1.5rem 1rem; }
    .add-item-modal-body { padding: 1.5rem; }
    .form-section { padding: 1rem; }
    .modal-title-container { gap: 0.75rem; }
    .modal-icon { width: 50px; height: 50px; font-size: 24px; }
    .add-item-modal-header .modal-title { font-size: 1.25rem; }
    
    /* Preset Modal */
    #presetModal .kms-modal-dialog { margin: 0.5rem; }
    #presetModal .col-6,
    #presetModal .col-4 {
        grid-column: span 12;
    }
    
    /* Credits History Modal */
    #creditsHistoryModal .kms-modal-dialog { margin: 0.5rem; }
    .credits-stats-grid { grid-template-columns: 1fr; }
    .credits-history-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    .credits-item-date {
        margin-left: 0;
        text-align: left;
    }
    
    /* Toast Messages */
    .toast-message {
        left: 10px;
        right: 10px;
        min-width: auto;
    }
}

/* =================================== */
/* ===== Save Configuration Modal ===== */
/* =================================== */

/* Save Configuration Modal specific styles */
#saveConfigModal .kms-modal-dialog {
    width: min(600px, 95vw);
    max-height: 85vh;
}

#saveConfigModal .kms-modal-body {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    min-height: 400px;
}

#saveConfigModal .card {
    border: none;
    background: transparent;
    height: 100%;
    display: flex;
    flex-direction: column;
}

#saveConfigModal .card-header {
    background: transparent;
    border: none;
    padding: 0 0 1rem 0;
    margin-bottom: 1rem;
    border-bottom: 2px solid var(--kms-border-color);
}

#saveConfigModal .card-header h3 {
    color: var(--kms-text-dark);
    font-weight: 600;
    font-size: 1.25rem;
    margin: 0;
}

#saveConfigModal .card-body {
    padding: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Form section */
#saveConfigModal .form-grid {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

#saveConfigModal .col-12 {
    grid-column: span 12;
}

/* Enhanced input styles */
#saveConfigModal .kms-label {
    display: block;
    font-weight: 600;
    color: var(--kms-text-dark);
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

#saveConfigModal .kms-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--kms-border-light);
    border-radius: 8px;
    background: var(--kms-text-light);
    color: var(--kms-text-dark);
    font-size: 1rem;
    transition: all 0.3s ease;
    resize: none;
    font-family: inherit;
}

#saveConfigModal .kms-input:focus {
    outline: none;
    border-color: var(--kms-primary);
    box-shadow: 0 0 0 3px rgba(var(--kms-primary-rgb, 74, 144, 226), 0.1);
    transform: translateY(-1px);
}

#saveConfigModal .kms-input:hover:not(:focus) {
    border-color: var(--kms-border-color);
}

/* Auto-resize textarea */
#saveConfigModal textarea.kms-input {
    min-height: 80px;
    max-height: 200px;
    overflow-y: auto;
    line-height: 1.5;
}

/* Enhanced Configuration Form Styles */
.config-form-enhanced {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.config-input-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
    gap: 5px;
}

.config-label {
    font-weight: 600;
    color: var(--text-color-1, #333);
    margin-bottom: 0;
    font-size: 15px;
    letter-spacing: 0.3px;
}

.config-input, .config-textarea {
    width: 100%;
    padding: 14px 18px;
    border: 2px solid var(--border-color-1, #e1e5e9);
    border-radius: 10px;
    font-size: 15px;
    line-height: 1.5;
    background-color: var(--color-2, #ffffff);
    transition: all 0.3s ease;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
    font-family: inherit;
}

.config-input:focus, .config-textarea:focus {
    outline: none;
    border-color: var(--color-1, #0095ff);
    box-shadow: 0 0 0 4px rgba(0, 149, 255, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
    background-color: var(--color-5, #f8f9fa);
    transform: translateY(-1px);
}

.config-textarea {
    resize: vertical;
    min-height: 100px;
    font-family: inherit;
}

.config-input::placeholder, .config-textarea::placeholder {
    color: var(--text-color-4, #6c757d);
    opacity: 0.7;
}

/* Button area - fixed at bottom right */
#saveConfigModal .save-config-buttons {
    display: flex;
    justify-content: flex-end !important;
    gap: 0.75rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--kms-border-light);
    margin-top: auto;
    flex-direction: row !important;
}

#saveConfigModal .btn-action {
    min-width: 100px;
    padding: 10px 20px;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.2s ease;
    cursor: pointer;
    border: 2px solid transparent;
    font-size: 0.95rem;
}

#saveConfigModal .btn-action-cancel {
    background: var(--kms-bg-widget);
    color: var(--kms-text-muted);
    border-color: var(--kms-border-light);
}

#saveConfigModal .btn-action-cancel:hover {
    background: #f8f9fa;
    color: var(--kms-text-dark);
    border-color: var(--kms-border-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#saveConfigModal .btn-action-save {
    background: linear-gradient(135deg, var(--kms-primary) 0%, #3a7bd5 100%);
    color: var(--kms-text-light);
    border-color: var(--kms-primary);
    box-shadow: 0 2px 8px rgba(var(--kms-primary-rgb, 74, 144, 226), 0.3);
}

#saveConfigModal .btn-action-save:hover {
    background: linear-gradient(135deg, #3a7bd5 0%, var(--kms-primary) 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--kms-primary-rgb, 74, 144, 226), 0.4);
}

#saveConfigModal .btn-action-save:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(var(--kms-primary-rgb, 74, 144, 226), 0.3);
}

/* Loading state */
#saveConfigModal .btn-action.loading {
    position: relative;
    color: transparent;
    pointer-events: none;
}

#saveConfigModal .btn-action.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #saveConfigModal .kms-modal-dialog {
        width: 95vw;
        margin: 1rem;
    }

    #saveConfigModal .kms-modal-body {
        padding: 1rem;
    }

    #saveConfigModal .save-config-buttons {
        flex-direction: column-reverse !important;
        justify-content: center !important;
        gap: 0.5rem;
    }

    #saveConfigModal .btn-action {
        width: 100%;
        min-width: auto;
    }
}

/* =================================== */
/* ===== Form Layout Enhancements ===== */
/* =================================== */

/* 按鈕右對齊樣式 */
.toolbar-row.justify-end {
    display: flex;
    justify-content: flex-end !important;
    gap: 0.75rem;
}

/* Configuration 表單居中和放寬樣式 */
.config-form-centered {
    max-width: 800px;
    margin: 0 auto;
    padding: 1rem 2rem;
}

.config-input-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 1.5rem;
}

.config-input-container .kms-label {
    text-align: center;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--kms-text-dark);
}

.config-input-wide {
    width: 200% !important;
    max-width: 600px;
    min-width: 400px;
    padding: 12px 16px !important;
    font-size: 1rem;
    border-radius: 12px !important;
    border: 2px solid var(--kms-border-light) !important;
    background: var(--kms-secondary) !important;
    color: var(--kms-text-dark) !important;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.config-input-wide:focus {
    border-color: var(--kms-border-focus) !important;
    box-shadow: 0 0 0 0.25rem var(--kms-link-bg), 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-1px);
}

.config-input-wide:hover:not(:focus) {
    border-color: var(--kms-border-hover, #a0a0a0) !important;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
}

/* 響應式調整 */
@media (max-width: 768px) {
    .config-form-centered {
        padding: 0.5rem 1rem;
        max-width: 100%;
    }
    
    .config-input-wide {
        width: 100% !important;
        min-width: auto;
        max-width: 100%;
    }
    
    .toolbar-row.justify-end {
        flex-direction: column-reverse;
        align-items: stretch;
        gap: 0.5rem;
    }
    
    .toolbar-row.justify-end .btn-action {
        width: 100%;
        justify-content: center;
    }
}