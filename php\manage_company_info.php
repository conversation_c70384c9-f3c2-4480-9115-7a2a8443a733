<?php
session_start();
require_once 'CompanyInfoManager.php';
require_once 'Response.php';

header('Content-Type: application/json');
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// 檢查用戶是否已登入
if (!isset($_SESSION['user_id'])) {
    Response::errorAndExit('請先登入');
    exit;
}

$userId = $_SESSION['user_id'];
$companyManager = new CompanyInfoManager();

try {
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            // 獲取公司資訊
            $result = $companyManager->getCompanyInfo($userId);
            echo json_encode($result);
            break;
            
        case 'POST':
            // 保存公司資訊
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                Response::errorAndExit('無效的請求數據');
                exit;
            }
            
            $companyName = trim($input['company_name'] ?? '');
            $companyWebsite = trim($input['company_website'] ?? '');
            $companyPhone = trim($input['company_phone'] ?? '');
            $companyEmail = trim($input['company_email'] ?? '');
            
            // 驗證輸入數據
            $validation = $companyManager->validateCompanyInfo($companyName, $companyWebsite, $companyPhone, $companyEmail);
            
            if (!$validation['success']) {
                echo json_encode($validation);
                exit;
            }
            
            // 保存公司資訊
            $result = $companyManager->saveCompanyInfo($userId, $companyName, $companyWebsite, $companyPhone, $companyEmail);
            echo json_encode($result);
            break;
            
        case 'DELETE':
            // 刪除公司資訊
            $result = $companyManager->deleteCompanyInfo($userId);
            echo json_encode($result);
            break;
            
        default:
            Response::errorAndExit('不支援的請求方法');
            break;
    }
    
} catch (Exception $e) {
    error_log("manage_company_info.php Error: " . $e->getMessage());
    Response::errorAndExit('系統錯誤');
}
?>