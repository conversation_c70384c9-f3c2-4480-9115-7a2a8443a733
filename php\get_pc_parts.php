<?php
/**
 * 獲取電腦零件列表
 * KMS PC Receipt Maker
 */

session_start();

require_once 'ReceiptManager.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

// 檢查用戶是否已登入
if (!isset($_SESSION['user_id'])) {
    Response::errorAndExit('請先登入', 401);
}

$userId = $_SESSION['user_id'];

try {
    $receiptManager = new ReceiptManager();
    
    // 獲取分類參數
    $category = isset($_GET['category']) ? trim($_GET['category']) : '';
    
    // 獲取電腦零件列表（添加用戶過濾）
    $pcParts = $receiptManager->getPcParts($category, $userId);
    
    Response::successAndExit($pcParts, '電腦零件列表獲取成功');
    
} catch (Exception $e) {
    Response::errorAndExit('獲取電腦零件列表失敗: ' . $e->getMessage());
}
?>
