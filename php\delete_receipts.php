<?php
/**
 * 批量刪除收據
 * KMS PC Receipt Maker
 */

session_start();

require_once 'ReceiptManager.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

// 檢查用戶是否已登入
if (!isset($_SESSION['user_id'])) {
    Response::errorAndExit('請先登入', 401);
}

$userId = $_SESSION['user_id'];

try {
    // 只允許 POST 請求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        Response::errorAndExit('只允許 POST 請求', 405);
    }

    // 獲取 JSON 輸入
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['ids']) || !is_array($input['ids'])) {
        Response::errorAndExit('缺少收據 ID 列表', 400);
    }
    
    $receiptIds = array_map('intval', $input['ids']);
    $receiptIds = array_filter($receiptIds, function($id) {
        return $id > 0;
    });
    
    if (empty($receiptIds)) {
        Response::errorAndExit('沒有有效的收據 ID', 400);
    }
    
    $receiptManager = new ReceiptManager();
    $result = $receiptManager->deleteReceipts($receiptIds, $userId);
    
    if ($result) {
        Response::successAndExit([
            'deleted_count' => count($receiptIds)
        ], '收據批量刪除成功');
    } else {
        Response::errorAndExit('收據批量刪除失敗');
    }
    
} catch (Exception $e) {
    error_log('Delete receipts error: ' . $e->getMessage());
    Response::errorAndExit('批量刪除收據時發生錯誤: ' . $e->getMessage());
}
?>