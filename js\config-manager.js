/**
 * 配置管理模組
 * KMS PC Receipt Maker
 */

// 配置管理類
class ConfigurationManager {
    constructor() {
        this.savedConfigurations = [];
    }

    /**
     * 保存收據配置
     */
    async saveReceiptConfiguration() {
        // 使用 KMS 樣式的模態框而不是 custom-modal
        showSaveConfigurationModal();
        return;
    }

    /**
     * 實際保存配置的函數
     */
    async saveConfigurationWithName(configName) {
        if (!configName || configName.trim() === '') {
            return;
        }

        const configuration = {
            name: configName.trim(),
            customerInfo: {
                name: document.getElementById('customerName').value.trim(),
                phone: document.getElementById('customerPhone').value.trim(),
                email: document.getElementById('customerEmail').value.trim(),
                address: document.getElementById('customerAddress').value.trim()
            },
            paymentMethod: 'cash', // Default payment method since no paymentMethod field exists
            items: window.ItemManager ? [...window.ItemManager.receiptItems] : [...(window.receiptItems || [])],
            discountValue: parseFloat(document.getElementById('discountValue')?.value) || 0,
            discountType: document.getElementById('discountType')?.value || 'amount',
            taxRate: parseFloat(document.getElementById('taxRate').value) || 0,
            notes: document.getElementById('notes').value.trim()
        };

        try {
            const response = await fetch('php/save_configuration.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(configuration)
            });

            const data = await response.json();

            if (data.success) {
                showMessage(LanguageManager.getText('config_saved_success') || '配置保存成功！', 'success');
            } else {
                showMessage(data.message || LanguageManager.getText('config_save_failed'), 'error');
            }
        } catch (error) {
            console.error('Error saving configuration:', error);
            showMessage(getText('config_save_error') || 'Error saving configuration', 'error');
        }
    }

    /**
     * 載入收據配置
     */
    async loadReceiptConfiguration(configId) {
        try {
            const response = await fetch(`php/get_configurations.php`);
            const data = await response.json();

            if (!data.success) {
                showMessage(data.message || LanguageManager.getText('config_load_failed'), 'error');
                return;
            }

            const config = data.data.find(c => c.id == configId);
            
            if (!config) {
                showMessage(LanguageManager.getText('config_not_found') || '找不到指定的配置', 'error');
                return;
            }

            // 載入客戶信息
            document.getElementById('customerName').value = config.customerInfo.name || '';
            document.getElementById('customerPhone').value = config.customerInfo.phone || '';
            document.getElementById('customerEmail').value = config.customerInfo.email || '';
            document.getElementById('customerAddress').value = config.customerInfo.address || '';
            
            // 載入付款方式 (paymentMethod element doesn't exist, so skip this)
            // document.getElementById('paymentMethod').value = config.payment_method || 'cash';
            
            // 載入項目 - ensure proper number types and unique IDs
            if (window.ItemManager) {
                // Clear existing items first
                window.ItemManager.setReceiptItems([]);

                // Add items with unique IDs and proper event binding
                config.items.forEach((item, index) => {
                    const receiptItem = {
                        ...item,
                        id: Date.now() + index + Math.random(), // Generate unique ID
                        quantity: parseInt(item.quantity) || 1,
                        unitPrice: parseFloat(item.unitPrice) || 0,
                        totalPrice: parseFloat(item.totalPrice) || 0,
                        originalPrice: parseFloat(item.originalPrice) || 0,
                        specialPrice: parseFloat(item.specialPrice) || 0,
                        discountPercent: parseInt(item.discountPercent) || 0,
                        hidePrice: Boolean(item.hidePrice || false)
                    };

                    // Add to receiptItems array
                    window.ItemManager.receiptItems.push(receiptItem);
                });

                // Update display and totals after all items are added
                window.ItemManager.updateReceiptItemsDisplay();
                window.ItemManager.updateTotals();
            } else {
                window.receiptItems = [...config.items];
                if (window.updateItemsList) {
                    window.updateItemsList();
                }
            }
            
            // 載入折扣、稅率和備註
            const discountValueElement = document.getElementById('discountValue');
            const discountTypeElement = document.getElementById('discountType');
            if (discountValueElement) {
                discountValueElement.value = config.discount_value || config.discount_amount || 0;
            }
            if (discountTypeElement) {
                discountTypeElement.value = config.discount_type || 'amount';
            }
            document.getElementById('taxRate').value = config.tax_rate || 0;
            document.getElementById('notes').value = config.notes || '';
            
            // 重新計算總計
            if (window.calculateTotals) {
                window.calculateTotals();
            }
            
            // 關閉模態框
            const modal = Modal.getInstance(document.getElementById('configurationModal'));
            if (modal) {
                modal.hide();
            }
            
            showMessage(LanguageManager.getText('config_loaded_success') || '配置載入成功！', 'success');
        } catch (error) {
            console.error('Error loading configuration:', error);
            showMessage('載入配置時發生錯誤', 'error');
        }
    }

    /**
     * 顯示配置管理模態框
     */
    async showConfigurationModal() {
        await this.loadConfigurationList();
        const modal = Modal.getInstance(document.getElementById('configurationModal'));
        modal.show();
    }

    /**
     * 獲取已保存的配置
     */
    async getSavedConfigurations() {
        try {
            const response = await fetch('php/get_configurations.php');
            const data = await response.json();
            
            if (data.success) {
                return data.data;
            } else {
                console.error('Error loading configurations:', data.message);
                return [];
            }
        } catch (error) {
            console.error('Error loading configurations:', error);
            return [];
        }
    }

    /**
     * 載入配置列表
     */
    async loadConfigurationList() {
        const configurations = await this.getSavedConfigurations();
        const container = document.getElementById('configurationList');
        
        if (configurations.length === 0) {
            container.innerHTML = `<div class="configuration-empty">${getText('no_saved_configurations')}</div>`;
            return;
        }
        
        let html = '';
        configurations.forEach(config => {
            const createdDate = new Date(config.created_at).toLocaleString();
            const itemCount = Array.isArray(config.items) ? config.items.length : 0;
            const customerName = config.customerInfo && config.customerInfo.name ? config.customerInfo.name : '未設定';
            
            html += `
                <div class="configuration-item">
                    <div class="configuration-item-main">
                        <div class="configuration-title-line">
                            <span class="configuration-name">${config.name}</span>
                            <span class="configuration-date">${createdDate}</span>
                        </div>
                        <div class="configuration-desc">
                            項目數量: ${itemCount} | 客戶: ${customerName}
                        </div>
                    </div>
                    <div class="configuration-item-actions">
                        <button type="button" class="btn-action btn-action-select" onclick="configManager.loadReceiptConfiguration(${config.id})" title="載入配置">
                            <span class="icon-symbol">📥</span><span class="btn-text">載入</span>
                        </button>
                        <button type="button" class="btn-action btn-action-delete" onclick="configManager.deleteConfiguration(${config.id})" title="刪除配置">
                            <span class="icon-symbol">🗑</span><span class="btn-text">刪除</span>
                        </button>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }

    /**
     * 刪除配置
     */
    async deleteConfiguration(configId) {
        const confirmed = await showConfirm(
            LanguageManager.getText('confirm_delete_config') || '確定要刪除此配置嗎？此操作無法復原。',
            '刪除配置'
        );

        if (!confirmed) {
            return;
        }
        
        try {
            const formData = new FormData();
            formData.append('id', configId);

            const response = await fetch('php/delete_configuration.php', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                await this.loadConfigurationList();
                showMessage(LanguageManager.getText('config_deleted_success') || '配置刪除成功！', 'success');
            } else {
                showMessage(data.message || LanguageManager.getText('config_delete_failed'), 'error');
            }
        } catch (error) {
            console.error('Error deleting configuration:', error);
            showMessage('刪除配置時發生錯誤', 'error');
        }
    }
}

// 創建全局實例
const configManager = new ConfigurationManager();

// 新的模態框管理函數
function showConfigurationModal() {
    const modal = document.getElementById('configurationModal');
    if (modal) {
        modal.style.display = 'flex';
        modal.setAttribute('aria-hidden', 'false');
        loadConfigurationList();
    }
}

function hideConfigurationModal() {
    const modal = document.getElementById('configurationModal');
    if (modal) {
        modal.style.display = 'none';
        modal.setAttribute('aria-hidden', 'true');
        hideAddConfigForm();
    }
}

function showAddConfigForm() {
    const form = document.getElementById('addConfigForm');
    if (form) {
        form.classList.remove('add-preset-form-hidden');
        form.classList.add('add-preset-form-visible');
        document.getElementById('configName').focus();
    }
}

function hideAddConfigForm() {
    const form = document.getElementById('addConfigForm');
    if (form) {
        form.classList.remove('add-preset-form-visible');
        form.classList.add('add-preset-form-hidden');
        // 清空表單
        document.getElementById('configName').value = '';
        document.getElementById('configDescription').value = '';
    }
}

function cancelAddConfig() {
    hideAddConfigForm();
}

function saveConfigItem() {
    const name = document.getElementById('configName').value.trim();
    const description = document.getElementById('configDescription').value.trim();

    if (!name) {
        showMessage(LanguageManager.getText('config_name_required') || '請輸入配置名稱', 'error');
        return;
    }

    // 使用現有的保存配置功能
    configManager.saveReceiptConfiguration(name, description);
    hideAddConfigForm();
}

function loadConfigurationList() {
    // 使用現有的配置加載功能
    if (configManager && typeof configManager.loadSavedConfigurations === 'function') {
        configManager.loadSavedConfigurations();
    } else {
        // 如果函數不存在，顯示空狀態
        const configList = document.getElementById('configurationList');
        if (configList) {
            configList.innerHTML = '<div class="empty-state"><p>No configurations saved yet</p></div>';
        }
    }
}

// KMS 樣式的保存配置模態框
function showSaveConfigurationModal() {
    // 創建優化的保存配置模態框
    const modalHTML = `
        <div id="saveConfigModal" class="kms-modal" aria-hidden="true" role="dialog" aria-modal="true" aria-labelledby="saveConfigModalTitle">
            <div class="kms-modal-dialog" role="document">
                <header class="kms-modal-header">
                    <h5 class="kms-modal-title" id="saveConfigModalTitle" data-lang="save_configuration">Save Configuration</h5>
                    <div class="toolbar-row">
                        <button type="button" class="kms-modal-close" onclick="hideSaveConfigurationModal()" aria-label="Close">×</button>
                    </div>
                </header>
                <div class="kms-modal-body">
                    <div class="card">
                        <header class="card-header">
                            <h3 class="mb-0" data-lang="save_configuration_title">Save Configuration</h3>
                        </header>
                        <div class="card-body">
                            <div class="form-grid config-form-enhanced">
                                <div class="col-12 config-input-group">
                                    <label class="kms-label config-label" for="saveConfigName" data-lang="config_name">Configuration Name</label>
                                    <input type="text" class="kms-input config-input" id="saveConfigName" required
                                           placeholder="Enter configuration name"
                                           data-lang-placeholder="config_name_placeholder">
                                </div>
                                <div class="col-12 config-input-group">
                                    <label class="kms-label config-label" for="saveConfigDescription" data-lang="config_description">Configuration Description</label>
                                    <textarea class="kms-input config-textarea" id="saveConfigDescription" rows="4"
                                              placeholder="Enter description (optional)"
                                              data-lang-placeholder="config_description_placeholder"></textarea>
                                </div>
                            </div>
                            <div class="save-config-buttons" style="justify-content: flex-end;">
                                <button type="button" class="btn-action btn-action-cancel" onclick="hideSaveConfigurationModal()" data-lang="cancel">Cancel</button>
                                <button type="button" class="btn-action btn-action-save" onclick="executeSaveConfiguration()" data-lang="save">Save</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除現有的模態框
    const existingModal = document.getElementById('saveConfigModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新的模態框
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // 顯示模態框
    const modal = document.getElementById('saveConfigModal');
    modal.setAttribute('aria-hidden', 'false');
    modal.style.display = 'flex';

    // 聚焦到輸入框並設置自動調整功能
    setTimeout(() => {
        const nameInput = document.getElementById('saveConfigName');
        const descriptionInput = document.getElementById('saveConfigDescription');

        if (nameInput) {
            nameInput.focus();
        }

        // 設置 textarea 自動調整高度
        if (descriptionInput) {
            setupAutoResizeTextarea(descriptionInput);
        }
    }, 100);
}

function hideSaveConfigurationModal() {
    const modal = document.getElementById('saveConfigModal');
    if (modal) {
        modal.setAttribute('aria-hidden', 'true');
        modal.style.display = 'none';
        modal.remove();
    }
}

function executeSaveConfiguration() {
    const nameInput = document.getElementById('saveConfigName');
    const descriptionInput = document.getElementById('saveConfigDescription');
    const saveButton = document.querySelector('#saveConfigModal .btn-action-save');

    const configName = nameInput ? nameInput.value.trim() : '';
    const configDescription = descriptionInput ? descriptionInput.value.trim() : '';

    if (!configName) {
        // 高亮顯示錯誤的輸入框
        if (nameInput) {
            nameInput.style.borderColor = '#dc3545';
            nameInput.focus();
            setTimeout(() => {
                nameInput.style.borderColor = '';
            }, 3000);
        }

        if (window.UIManager && typeof window.UIManager.showMessage === 'function') {
            window.UIManager.showMessage(
                LanguageManager.getText('config_name_required') || 'Please enter a configuration name',
                'error'
            );
        }
        return;
    }

    // 顯示加載狀態
    if (saveButton) {
        saveButton.classList.add('loading');
        saveButton.disabled = true;
    }

    // 執行保存
    try {
        configManager.saveConfigurationWithName(configName, configDescription);

        // 延遲隱藏模態框以顯示成功效果
        setTimeout(() => {
            hideSaveConfigurationModal();
        }, 500);
    } catch (error) {
        // 恢復按鈕狀態
        if (saveButton) {
            saveButton.classList.remove('loading');
            saveButton.disabled = false;
        }

        if (window.UIManager && typeof window.UIManager.showMessage === 'function') {
            window.UIManager.showMessage(
                LanguageManager.getText('save_failed') || 'Failed to save configuration',
                'error'
            );
        }
    }
}

// 設置 textarea 自動調整高度的函數
function setupAutoResizeTextarea(textarea) {
    if (!textarea) return;

    // 設置初始高度
    textarea.style.height = 'auto';
    textarea.style.height = Math.max(80, textarea.scrollHeight) + 'px';

    // 監聽輸入事件
    textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        const newHeight = Math.min(Math.max(80, this.scrollHeight), 200);
        this.style.height = newHeight + 'px';
    });

    // 監聽粘貼事件
    textarea.addEventListener('paste', function() {
        setTimeout(() => {
            this.style.height = 'auto';
            const newHeight = Math.min(Math.max(80, this.scrollHeight), 200);
            this.style.height = newHeight + 'px';
        }, 10);
    });
}

// 導出到全局作用域
window.configManager = configManager;
window.saveReceiptConfiguration = () => configManager.saveReceiptConfiguration();
window.showConfigurationModal = showConfigurationModal;
window.hideConfigurationModal = hideConfigurationModal;
window.showAddConfigForm = showAddConfigForm;
window.hideAddConfigForm = hideAddConfigForm;
window.cancelAddConfig = cancelAddConfig;
window.saveConfigItem = saveConfigItem;
window.loadConfiguration = (configId) => configManager.loadConfiguration(configId);
window.deleteConfiguration = (id) => configManager.deleteConfiguration(id);
window.showSaveConfigurationModal = showSaveConfigurationModal;
window.hideSaveConfigurationModal = hideSaveConfigurationModal;
window.executeSaveConfiguration = executeSaveConfiguration;
window.setupAutoResizeTextarea = setupAutoResizeTextarea;
