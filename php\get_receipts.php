<?php
/**
 * 獲取收據列表
 * KMS PC Receipt Maker
 */

session_start();

require_once 'ReceiptManager.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

// 檢查用戶是否已登入
if (!isset($_SESSION['user_id'])) {
    Response::errorAndExit('請先登入', 401);
}

$userId = $_SESSION['user_id'];

try {
    $receiptManager = new ReceiptManager();
    
    // 獲取分頁參數
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
    
    // 獲取篩選參數
    $filters = [];
    if (!empty($_GET['search'])) {
        $filters['search'] = trim($_GET['search']);
    }
    if (!empty($_GET['payment_method'])) {
        $filters['payment_method'] = $_GET['payment_method'];
    }
    if (!empty($_GET['date_from'])) {
        $filters['date_from'] = $_GET['date_from'];
    }
    if (!empty($_GET['date_to'])) {
        $filters['date_to'] = $_GET['date_to'];
    }
    
    // 獲取收據列表（添加用戶隔離）
    $result = $receiptManager->getReceipts($page, $limit, $filters, $userId);
    
    Response::successAndExit($result, '收據列表獲取成功');
    
} catch (Exception $e) {
    error_log('Get receipts error: ' . $e->getMessage());
    Response::errorAndExit('獲取收據列表失敗: ' . $e->getMessage());
}
?>