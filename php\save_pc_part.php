<?php
/**
 * 新增/更新 電腦零件（預設項目）
 * KMS PC Receipt Maker
 */

session_start();

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

// 檢查用戶是否已登入
if (!isset($_SESSION['user_id'])) {
    Response::errorAndExit('請先登入', 401);
}

$currentUserId = $_SESSION['user_id'];

try {
    $input = json_decode(file_get_contents('php://input'), true);
    if (!is_array($input)) {
        Response::errorAndExit('無效的請求內容');
    }

    // 必填: name, category
    $name = isset($input['name']) ? trim($input['name']) : '';
    $category = isset($input['category']) ? trim($input['category']) : '';
    if ($name === '' || $category === '') {
        Response::errorAndExit('缺少必要欄位 name 或 category');
    }

    $description = isset($input['description']) ? trim($input['description']) : null;
    // default_price 欄位為 NOT NULL，若未提供則使用 0.00
    $default_price = isset($input['default_price']) && $input['default_price'] !== '' ? floatval($input['default_price']) : 0.00;
    $original_price = isset($input['original_price']) && $input['original_price'] !== '' ? floatval($input['original_price']) : null;
    $special_price = isset($input['special_price']) && $input['special_price'] !== '' ? floatval($input['special_price']) : null;

    $db = new Database();

    // 更新
    if (!empty($input['id'])) {
        $id = intval($input['id']);
        // 檢查存在且屬於當前用戶或管理員
        $exists = $db->fetch('SELECT id, created_by FROM pc_parts WHERE id = ?', [$id]);
        if (!$exists) {
            Response::errorAndExit('預設項目不存在');
        }
        
        // 檢查權限：只有項目創建者可以修改（包括管理員也只能修改自己的項目）
        if ($exists['created_by'] != $currentUserId) {
            Response::errorAndExit('沒有權限修改此項目', 403);
        }

        $sql = 'UPDATE pc_parts SET name = ?, category = ?, description = ?, default_price = ?, original_price = ?, special_price = ? WHERE id = ?';
        $params = [
            $name,
            $category,
            $description,
            $default_price,
            $original_price,
            $special_price,
            $id
        ];
        $db->update($sql, $params);

        Response::successAndExit(['id' => $id], '預設項目更新成功');
    }

    // 插入: sort_order = MAX(sort_order)+1
    $max = $db->fetch('SELECT MAX(sort_order) AS max_order FROM pc_parts');
    $nextOrder = isset($max['max_order']) && $max['max_order'] !== null ? (intval($max['max_order']) + 1) : 1;

    $sql = 'INSERT INTO pc_parts (name, category, description, default_price, original_price, special_price, sort_order, is_active, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, 1, ?)';
    $params = [
        $name,
        $category,
        $description,
        $default_price,
        $original_price,
        $special_price,
        $nextOrder,
        $currentUserId
    ];

    $newId = $db->insert($sql, $params);
    Response::successAndExit(['id' => $newId], '預設項目新增成功');

} catch (Exception $e) {
    Response::errorAndExit('保存預設項目失敗: ' . $e->getMessage());
}
