<?php
/**
 * 用戶刪除管理器
 * KMS Receipt Maker - User Deletion Manager
 * 
 * 處理用戶刪除及其相關數據的級聯刪除
 */

class UserDeletionManager {
    private $db;
    private $conn;
    
    public function __construct() {
        require_once 'DatabaseMySQLi.php';
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }
    
    /**
     * 完全刪除用戶及其所有相關數據
     */
    public function deleteUserCompletely($userId, $deletedBy = null) {
        try {
            $this->conn->begin_transaction();
            
            // 獲取用戶信息
            $userInfo = $this->getUserInfo($userId);
            if (!$userInfo) {
                throw new Exception('User not found');
            }
            
            // 防止刪除管理員（除非是自己刪除自己）
            if ($userInfo['user_type'] === 'admin' && $deletedBy !== $userId) {
                throw new Exception('Cannot delete admin user');
            }
            
            // 記錄刪除操作
            $this->logDeletionOperation($userId, $userInfo, $deletedBy);
            
            // 1. 刪除收據項目
            $this->deleteReceiptItems($userId);
            
            // 2. 刪除收據主記錄
            $this->deleteReceipts($userId);
            
            // 3. 刪除預設項目
            $this->deletePresets($userId);
            
            // 4. 刪除配置
            $this->deleteConfigurations($userId);
            
            // 5. 刪除點數記錄
            $this->deleteCreditRecords($userId);
            
            // 6. 刪除郵件日誌
            $this->deleteEmailLogs($userId);
            
            // 7. 刪除代理登入日誌
            $this->deleteProxyLogs($userId);
            
            // 8. 刪除其他相關數據
            $this->deleteOtherUserData($userId);
            
            // 9. 最後刪除用戶主記錄
            $this->deleteUserRecord($userId);
            
            $this->conn->commit();
            
            return [
                'success' => true,
                'message' => 'User and all related data deleted successfully',
                'deleted_user' => $userInfo
            ];
            
        } catch (Exception $e) {
            $this->conn->rollback();
            error_log("User deletion error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 獲取用戶信息
     */
    private function getUserInfo($userId) {
        $stmt = $this->conn->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->bind_param('i', $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        return $result->fetch_assoc();
    }
    
    /**
     * 記錄刪除操作
     */
    private function logDeletionOperation($userId, $userInfo, $deletedBy) {
        $stmt = $this->conn->prepare("
            INSERT INTO user_deletion_logs (
                deleted_user_id, deleted_username, deleted_email, deleted_user_type,
                deleted_by, deletion_reason, ip_address, user_agent, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $deletionReason = $deletedBy === $userId ? 'self_deletion' : 'admin_deletion';
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $stmt->bind_param('isssssss', 
            $userId, 
            $userInfo['username'], 
            $userInfo['email'], 
            $userInfo['user_type'],
            $deletedBy, 
            $deletionReason, 
            $ipAddress, 
            $userAgent
        );
        $stmt->execute();
    }
    
    /**
     * 刪除收據項目
     */
    private function deleteReceiptItems($userId) {
        $stmt = $this->conn->prepare("
            DELETE ri FROM receipt_items ri 
            INNER JOIN receipts r ON ri.receipt_id = r.id 
            WHERE r.user_id = ?
        ");
        $stmt->bind_param('i', $userId);
        $stmt->execute();
    }
    
    /**
     * 刪除收據主記錄
     */
    private function deleteReceipts($userId) {
        $stmt = $this->conn->prepare("DELETE FROM receipts WHERE user_id = ?");
        $stmt->bind_param('i', $userId);
        $stmt->execute();
    }
    
    /**
     * 刪除預設項目
     */
    private function deletePresets($userId) {
        $stmt = $this->conn->prepare("DELETE FROM pc_parts WHERE created_by = ?");
        $stmt->bind_param('i', $userId);
        $stmt->execute();
    }
    
    /**
     * 刪除配置
     */
    private function deleteConfigurations($userId) {
        $stmt = $this->conn->prepare("DELETE FROM receipt_configurations WHERE user_id = ?");
        $stmt->bind_param('i', $userId);
        $stmt->execute();
    }
    
    /**
     * 刪除點數記錄
     */
    private function deleteCreditRecords($userId) {
        $stmt = $this->conn->prepare("DELETE FROM credit_transactions WHERE user_id = ?");
        $stmt->bind_param('i', $userId);
        $stmt->execute();
    }
    
    /**
     * 刪除郵件日誌
     */
    private function deleteEmailLogs($userId) {
        // 檢查表是否存在
        $result = $this->conn->query("SHOW TABLES LIKE 'email_logs'");
        if ($result->num_rows > 0) {
            $stmt = $this->conn->prepare("DELETE FROM email_logs WHERE user_id = ?");
            $stmt->bind_param('i', $userId);
            $stmt->execute();
        }
    }
    
    /**
     * 刪除代理登入日誌
     */
    private function deleteProxyLogs($userId) {
        // 檢查表是否存在
        $result = $this->conn->query("SHOW TABLES LIKE 'admin_proxy_logs'");
        if ($result->num_rows > 0) {
            // 刪除作為管理員的代理日誌
            $stmt = $this->conn->prepare("DELETE FROM admin_proxy_logs WHERE admin_id = ?");
            $stmt->bind_param('i', $userId);
            $stmt->execute();
            
            // 刪除作為目標用戶的代理日誌
            $stmt = $this->conn->prepare("DELETE FROM admin_proxy_logs WHERE target_user_id = ?");
            $stmt->bind_param('i', $userId);
            $stmt->execute();
        }
    }
    
    /**
     * 刪除其他用戶相關數據
     */
    private function deleteOtherUserData($userId) {
        // 刪除用戶會話數據（如果有的話）
        $result = $this->conn->query("SHOW TABLES LIKE 'user_sessions'");
        if ($result->num_rows > 0) {
            $stmt = $this->conn->prepare("DELETE FROM user_sessions WHERE user_id = ?");
            $stmt->bind_param('i', $userId);
            $stmt->execute();
        }
        
        // 刪除用戶設置（如果有的話）
        $result = $this->conn->query("SHOW TABLES LIKE 'user_settings'");
        if ($result->num_rows > 0) {
            $stmt = $this->conn->prepare("DELETE FROM user_settings WHERE user_id = ?");
            $stmt->bind_param('i', $userId);
            $stmt->execute();
        }
    }
    
    /**
     * 刪除用戶主記錄
     */
    private function deleteUserRecord($userId) {
        $stmt = $this->conn->prepare("DELETE FROM users WHERE id = ?");
        $stmt->bind_param('i', $userId);
        $stmt->execute();
    }
    
    /**
     * 獲取用戶相關數據統計
     */
    public function getUserDataStats($userId) {
        $stats = [];
        
        // 收據數量
        $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM receipts WHERE user_id = ?");
        $stmt->bind_param('i', $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        $stats['receipts'] = $result->fetch_assoc()['count'];
        
        // 預設項目數量
        $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM pc_parts WHERE created_by = ?");
        $stmt->bind_param('i', $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        $stats['presets'] = $result->fetch_assoc()['count'];
        
        // 配置數量
        $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM receipt_configurations WHERE user_id = ?");
        $stmt->bind_param('i', $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        $stats['configurations'] = $result->fetch_assoc()['count'];
        
        // 點數記錄數量
        $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM credit_transactions WHERE user_id = ?");
        $stmt->bind_param('i', $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        $stats['credit_transactions'] = $result->fetch_assoc()['count'];
        
        return $stats;
    }
}
?>
