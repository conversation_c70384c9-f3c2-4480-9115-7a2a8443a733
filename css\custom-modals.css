/* Custom Modal Styles */
.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.custom-modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.custom-modal {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    position: relative;
    transform: scale(0.8) translateY(50px);
    transition: all 0.3s ease;
}

.custom-modal-overlay.show .custom-modal {
    transform: scale(1) translateY(0);
}

.custom-modal-header {
    color: white;
    padding: 25px 30px;
    text-align: center;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.custom-modal-header h3 {
    margin: 0;
    font-size: 24px; /* 1.5rem */
    font-weight: 600;
}

.custom-modal-header .icon {
    font-size: 48px; /* 3rem */
    margin-bottom: 10px;
    opacity: 0.9;
}

.custom-modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    color: white;
    font-size: 24px; /* 1.5rem */
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.custom-modal-close:hover {
    opacity: 1;
}

.custom-modal-body {
    padding: 30px;
    text-align: center;
}

.custom-modal-body p {
    font-size: 24px;
    color: #ff0000;
    margin-bottom: 30px;
    line-height: 1.6;
}

.custom-modal-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.custom-modal-btn {
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-size: 16px; /* 1rem */
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.custom-modal-btn:hover {
    transform: translateY(-2px);
}

.custom-modal-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.custom-modal-btn-primary:hover {
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.custom-modal-btn-secondary {
    background: #f8f9fa;
    color: #6c757d;
    border: 2px solid #e9ecef;
}

.custom-modal-btn-secondary:hover {
    background: #e9ecef;
    transform: translateY(-1px); /* Note: Secondary button has a different hover transform */
}

.custom-modal-btn-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.custom-modal-btn-danger:hover {
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

/* Modal Type Variations */
.confirm-modal .custom-modal-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.confirm-modal .custom-modal-header .icon {
    color: #fff;
}

.success-modal .custom-modal-header {
    background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
}

.warning-modal .custom-modal-header {
    background: linear-gradient(135deg, #ffd43b 0%, #fab005 100%);
}

/* Input Modal Styles */
.custom-modal-input {
    width: 100%;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 16px; /* 1rem */
    margin-bottom: 20px;
    transition: border-color 0.3s ease;
}

.custom-modal-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Clear All Modal Specific Styles */
.clear-all-modal { /* Simplified selector */
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    border: none;
    color: white;
}

.clear-all-modal .custom-modal-header {
    background: transparent;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.clear-all-modal .custom-modal-header h3 {
    color: white;
    font-weight: 600;
}

.clear-all-modal .custom-modal-icon { /* Note: Original CSS had a typo, should probably be custom-modal-header .icon */
    color: white;
    font-size: 40px; /* 2.5rem */
    margin-bottom: 16px; /* 1rem */
}

.clear-all-modal .custom-modal-body {
    background: transparent;
    color: rgba(255, 255, 255, 0.95);
}

.clear-all-modal .custom-modal-message { /* Note: This class was not defined elsewhere, assuming it's a p tag */
    color: rgba(255, 255, 255, 0.95);
    font-size: 17.6px; /* 1.1rem */
    line-height: 1.5;
}

.clear-all-modal .custom-modal-btn-danger {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-weight: 600;
}

.clear-all-modal .custom-modal-btn-danger:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px); /* Explicitly add this for consistency */
    box-shadow: none; /* Reset box-shadow */
}

.clear-all-modal .custom-modal-btn-secondary {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.9);
    color: #ee5a52;
    font-weight: 600;
}

.clear-all-modal .custom-modal-btn-secondary:hover {
    background: white;
    border-color: white;
    transform: translateY(-2px); /* Explicitly add this for consistency */
    box-shadow: none; /* Reset box-shadow */
}


/* Animations */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(50px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes modalSlideOut {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.8) translateY(50px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .custom-modal {
        width: 95%;
        margin: 20px;
    }
    
    .custom-modal-header,
    .custom-modal-body {
        padding: 20px;
    }
    
    .custom-modal-buttons {
        flex-direction: column;
    }
    
    .custom-modal-btn {
        width: 100%;
    }
}