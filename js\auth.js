/**
 * 用戶認證相關JavaScript功能 - auth.js
 */

// 全局變數
let authCurrentLanguage = 'en';

// 語言包
const translations = {
    zh: {
        // 登入頁面
        login_title: '用戶登入',
        username: '用戶名/郵箱',
        password: '密碼',
        remember_me: '記住我',
        login_btn: '登入',
        register_link: '還沒有帳號？立即註冊',
        forgot_password: '忘記密碼？',
        
        // 註冊頁面
        register_title: '用戶註冊',
        email: '郵箱地址',
        first_name: '名字',
        last_name: '姓氏',
        phone: '電話號碼',
        confirm_password: '確認密碼',
        register_btn: '註冊',
        login_link: '已有帳號？立即登入',
        
        // 忘記密碼頁面
        forgot_password_title: '忘記密碼',
        forgot_password_desc: '請輸入您的郵箱地址，我們將發送密碼重置鏈接給您。',
        send_reset_link: '發送重置鏈接',
        back_to_login: '返回登入頁面',
        
        // 重置密碼頁面
        reset_password_title: '重置密碼',
        new_password: '新密碼',
        reset_password_btn: '重置密碼',
        
        // 郵件驗證
        verification_success_title: '郵箱驗證成功！',
        verification_success_message: '您的帳號已成功激活，您已獲得10個免費點數。現在可以登入了。',
        verification_failed_title: '驗證失敗',
        verification_failed_message: '驗證鏈接無效或已過期。鏈接可能已被使用或已過期。',
        login_now: '立即登入',
        go_home: '返回首頁',
        register_again: '重新註冊',
        try_login: '嘗試登入',

        // 通用
        loading: '載入中...',
        error: '錯誤',
        success: '成功'
    },
    en: {
        // 登入頁面
        login_title: 'User Login',
        username: 'Username/Email',
        password: 'Password',
        remember_me: 'Remember Me',
        login_btn: 'Login',
        register_link: 'Don\'t have an account? Register now',
        forgot_password: 'Forgot Password?',
        
        // 註冊頁面
        register_title: 'User Registration',
        email: 'Email Address',
        first_name: 'First Name',
        last_name: 'Last Name',
        phone: 'Phone Number',
        confirm_password: 'Confirm Password',
        register_btn: 'Register',
        login_link: 'Already have an account? Login now',
        
        // 忘記密碼頁面
        forgot_password_title: 'Forgot Password',
        forgot_password_desc: 'Please enter your email address and we will send you a password reset link.',
        send_reset_link: 'Send Reset Link',
        back_to_login: 'Back to Login',
        
        // 重置密碼頁面
        reset_password_title: 'Reset Password',
        new_password: 'New Password',
        reset_password_btn: 'Reset Password',
        
        // Email verification
        verification_success_title: 'Email Verified Successfully!',
        verification_success_message: 'Your account has been activated and you have received 10 free credits. You can now log in.',
        verification_failed_title: 'Verification Failed',
        verification_failed_message: 'Invalid or expired verification link. The link may have already been used or has expired.',
        login_now: 'Login Now',
        go_home: 'Go to Home',
        register_again: 'Register Again',
        try_login: 'Try Login',

        // 通用
        loading: 'Loading...',
        error: 'Error',
        success: 'Success'
    }
};

// DOM載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeAuth();
});

/**
 * 初始化認證頁面
 */
function initializeAuth() {
    // 從localStorage獲取語言設定
    const savedLanguage = localStorage.getItem('language') || 'en';
    changeAuthLanguage(savedLanguage);
    
    // 綁定表單事件
    bindFormEvents();
    
    // 綁定語言切換事件
    bindLanguageEvents();
    
    // 初始化表單驗證
    initializeFormValidation();
}

/**
 * 綁定表單事件
 */
function bindFormEvents() {
    const forms = document.querySelectorAll('.auth-form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                return false;
            }
            
            // 顯示載入狀態
            showLoadingState(this);
        });
    });
    
    // 密碼確認驗證
    const confirmPasswordField = document.getElementById('confirm_password');
    if (confirmPasswordField) {
        confirmPasswordField.addEventListener('input', validatePasswordMatchUpdated);
    }
    
    // 綁定密碼強度驗證
    const passwordInput = document.getElementById('password');
    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            validatePasswordStrength(this.value);
        });
    }
    
    // 即時表單驗證
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            clearFieldError(this);
        });
    });
}

/**
 * 綁定語言切換事件
 */
function bindLanguageEvents() {
    const langButtons = document.querySelectorAll('.kms-lang-btn');
    
    langButtons.forEach(button => {
        button.addEventListener('click', function() {
            const langCode = this.getAttribute('data-lang-code');
            changeAuthLanguage(langCode);
        });
    });
}

/**
 * 切換語言 (使用認證頁面專用的翻譯)
 */
function changeAuthLanguage(langCode) {
    if (!translations[langCode]) {
        console.warn('Language not supported:', langCode);
        return;
    }
    
    authCurrentLanguage = langCode;
    localStorage.setItem('language', langCode);
    
    // 更新所有帶有data-lang屬性的元素
    const elements = document.querySelectorAll('[data-lang]');
    elements.forEach(element => {
        const key = element.getAttribute('data-lang');
        if (translations[langCode] && translations[langCode][key]) {
            if (element.tagName === 'INPUT' && element.type === 'submit') {
                element.value = translations[langCode][key];
            } else if (element.tagName === 'INPUT' && element.hasAttribute('placeholder')) {
                element.placeholder = translations[langCode][key];
            } else {
                element.textContent = translations[langCode][key];
            }
        }
    });
    
    // 更新語言按鈕狀態
    const langButtons = document.querySelectorAll('.kms-lang-btn');
    langButtons.forEach(button => {
        const buttonLangCode = button.getAttribute('data-lang-code');
        if (buttonLangCode === langCode) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }
    });
}

/**
 * 初始化表單驗證
 */
function initializeFormValidation() {
    // 添加自定義驗證樣式
    const style = document.createElement('style');
    style.textContent = `
        .form-control.is-invalid {
            border-color: var(--color-4);
            box-shadow: 0 0 0 0.2rem rgba(255, 49, 49, 0.25);
        }
        .form-control.is-valid {
            border-color: var(--color-3);
            box-shadow: 0 0 0 0.2rem rgba(47, 255, 92, 0.25);
        }
        .invalid-feedback {
            display: block;
            color: var(--color-4);
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
    `;
    document.head.appendChild(style);
}

/**
 * 驗證表單
 */
function validateForm(form) {
    let isValid = true;
    const inputs = form.querySelectorAll('.form-control[required]');
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    // 特殊驗證：密碼確認
    const passwordField = form.querySelector('#password');
    const confirmPasswordField = form.querySelector('#confirm_password');
    
    if (passwordField && confirmPasswordField) {
        if (!validatePasswordMatch()) {
            isValid = false;
        }
    }
    
    return isValid;
}

/**
 * 驗證單個字段
 */
function validateField(field) {
    const value = field.value.trim();
    const fieldType = field.type;
    const fieldName = field.name;
    
    // 清除之前的錯誤
    clearFieldError(field);
    
    // 必填驗證
    if (field.hasAttribute('required') && !value) {
        showFieldError(field, getErrorMessage('required', fieldName));
        return false;
    }
    
    // 郵箱驗證
    if (fieldType === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            showFieldError(field, getErrorMessage('email'));
            return false;
        }
    }
    
    // 密碼長度驗證
    if (fieldName === 'password' && value && value.length < 6) {
        showFieldError(field, getErrorMessage('password_length'));
        return false;
    }
    
    // 用戶名長度驗證
    if (fieldName === 'username' && value && value.length < 3) {
        showFieldError(field, getErrorMessage('username_length'));
        return false;
    }
    
    // 驗證通過
    field.classList.add('is-valid');
    return true;
}

/**
 * 驗證密碼確認
 */
function validatePasswordMatch() {
    const passwordField = document.getElementById('password');
    const confirmPasswordField = document.getElementById('confirm_password');
    
    if (!passwordField || !confirmPasswordField) {
        return true;
    }
    
    clearFieldError(confirmPasswordField);
    
    if (passwordField.value !== confirmPasswordField.value) {
        showFieldError(confirmPasswordField, getErrorMessage('password_match'));
        return false;
    }
    
    confirmPasswordField.classList.add('is-valid');
    return true;
}

/**
 * 顯示字段錯誤
 */
function showFieldError(field, message) {
    field.classList.remove('is-valid');
    field.classList.add('is-invalid');
    
    // 移除舊的錯誤消息
    const existingError = field.parentNode.querySelector('.invalid-feedback');
    if (existingError) {
        existingError.remove();
    }
    
    // 添加新的錯誤消息
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    field.parentNode.appendChild(errorDiv);
}

/**
 * 清除字段錯誤
 */
function clearFieldError(field) {
    field.classList.remove('is-invalid', 'is-valid');
    
    const errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * 獲取錯誤消息
 */
function getErrorMessage(type, fieldName = '') {
    const messages = {
        zh: {
            required: 'This field is required',
            email: 'Please enter a valid email address',
                password_length: 'Password must be at least 6 characters',
                username_length: 'Username must be at least 3 characters',
            password_match: 'Passwords do not match'
        },
        en: {
            required: 'This field is required',
            email: 'Please enter a valid email address',
            password_length: 'Password must be at least 6 characters',
            username_length: 'Username must be at least 3 characters',
            password_match: 'Passwords do not match'
        }
    };
    
    return messages[authCurrentLanguage][type] || messages['en'][type];
}

/**
 * 顯示載入狀態
 */
function showLoadingState(form) {
    const submitButton = form.querySelector('button[type="submit"]');
    if (submitButton) {
        submitButton.classList.add('loading');
        submitButton.disabled = true;
        
        const originalText = submitButton.textContent;
        submitButton.textContent = translations[currentLanguage].loading;
        
        // 如果表單提交失敗，恢復按鈕狀態
        setTimeout(() => {
            submitButton.classList.remove('loading');
            submitButton.disabled = false;
            submitButton.textContent = originalText;
        }, 5000);
    }
}

/**
 * 顯示通知消息
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} notification`;
    notification.textContent = message;
    
    // 添加通知樣式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideInRight 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    // 自動移除通知
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

// 添加動畫樣式
const animationStyle = document.createElement('style');
animationStyle.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(animationStyle);

// 導出函數供全局使用
window.changeAuthLanguage = changeAuthLanguage;
window.showNotification = showNotification;

// 為了兼容性，也提供changeLanguage函數
window.changeLanguage = function(langCode) {
    // 如果存在LanguageManager，使用它
    if (window.LanguageManager && window.LanguageManager.changeLanguage) {
        window.LanguageManager.changeLanguage(langCode);
    } else {
        // 否則使用認證頁面的語言切換
        changeAuthLanguage(langCode);
    }
};

/**
 * 驗證密碼強度
 * @param {string} password - 密碼
 */
function validatePasswordStrength(password) {
    const requirements = {
        length: password.length >= 8,
        uppercase: /[A-Z]/.test(password),
        number: /[0-9]/.test(password),
        special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };
    
    // 更新規則顯示
    updatePasswordRule('rule-length', requirements.length);
    updatePasswordRule('rule-uppercase', requirements.uppercase);
    updatePasswordRule('rule-number', requirements.number);
    updatePasswordRule('rule-special', requirements.special);
    
    // 計算密碼強度
    const score = Object.values(requirements).filter(Boolean).length;
    const strengthElement = document.getElementById('passwordStrength');
    
    if (strengthElement) {
        if (score === 4) {
            strengthElement.textContent = window.LanguageManager ? LanguageManager.getText('password_strength_strong') : (authCurrentLanguage === 'zh' ? '密碼強度：強' : 'Password Strength: Strong');
            strengthElement.className = 'password-strength strong';
        } else if (score >= 2) {
            strengthElement.textContent = window.LanguageManager ? LanguageManager.getText('password_strength_medium') : (authCurrentLanguage === 'zh' ? '密碼強度：中等' : 'Password Strength: Medium');
            strengthElement.className = 'password-strength medium';
        } else {
            strengthElement.textContent = window.LanguageManager ? LanguageManager.getText('password_strength_weak') : (authCurrentLanguage === 'zh' ? '密碼強度：弱' : 'Password Strength: Weak');
            strengthElement.className = 'password-strength weak';
        }
    }
    
    // 返回是否所有規則都滿足
    return Object.values(requirements).every(req => req);
}

/**
 * 更新密碼規則顯示
 * @param {string} ruleId - 規則元素ID
 * @param {boolean} isValid - 是否有效
 */
function updatePasswordRule(ruleId, isValid) {
    const ruleElement = document.getElementById(ruleId);
    if (ruleElement) {
        const icon = ruleElement.querySelector('.rule-icon');
        
        if (isValid) {
            ruleElement.classList.remove('invalid');
            ruleElement.classList.add('valid');
            icon.textContent = '✓';
        } else {
            ruleElement.classList.remove('valid');
            ruleElement.classList.add('invalid');
            icon.textContent = '✗';
        }
    }
}

/**
 * 驗證密碼匹配（更新版本）
 */
function validatePasswordMatchUpdated() {
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    const matchElement = document.getElementById('passwordMatch');
    
    if (!passwordInput || !confirmPasswordInput || !matchElement) {
        return true;
    }
    
    const password = passwordInput.value;
    const confirmPassword = confirmPasswordInput.value;
    const messageElement = matchElement.querySelector('.match-message');
    
    if (confirmPassword === '') {
        matchElement.style.display = 'none';
        return true;
    }
    
    matchElement.style.display = 'block';
    
    if (password === confirmPassword) {
        matchElement.classList.remove('no-match');
        matchElement.classList.add('match');
        messageElement.textContent = window.LanguageManager ? LanguageManager.getText('password_match') : (authCurrentLanguage === 'zh' ? '密碼匹配' : 'Passwords match');
        return true;
    } else {
        matchElement.classList.remove('match');
        matchElement.classList.add('no-match');
        messageElement.textContent = window.LanguageManager ? LanguageManager.getText('password_no_match') : (authCurrentLanguage === 'zh' ? '密碼不匹配' : 'Passwords do not match');
        return false;
    }
}