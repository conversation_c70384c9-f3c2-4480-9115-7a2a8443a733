-- =====================================================
-- 添加郵件系統相關表
-- KMS Receipt Maker - Email System Tables
-- =====================================================

USE `kms_receipt_maker`;

-- =====================================================
-- Email logs table (email_logs)
-- 記錄所有發送的郵件日誌
-- =====================================================

CREATE TABLE IF NOT EXISTS `email_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Log ID',
    `recipient` varchar(100) NOT NULL COMMENT 'Recipient email address',
    `subject` varchar(255) NOT NULL COMMENT 'Email subject',
    `sent_at` datetime NOT NULL COMMENT 'Email sent time',
    `success` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Send success status',
    `error_message` text DEFAULT NULL COMMENT 'Error message if failed',
    `email_type` varchar(50) DEFAULT NULL COMMENT 'Email type (verification, password_reset, etc.)',
    `user_id` int(11) DEFAULT NULL COMMENT 'Related user ID',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Created time',
    
    PRIMARY KEY (`id`),
    KEY `idx_recipient` (`recipient`),
    KEY `idx_sent_at` (`sent_at`),
    KEY `idx_success` (`success`),
    KEY `idx_email_type` (`email_type`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_created_at` (`created_at`),
    
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Email logs table';

-- =====================================================
-- 更新 users 表，添加郵件驗證相關字段（如果不存在）
-- =====================================================

-- 檢查並添加 email_verify_token_expires 字段
ALTER TABLE `users` 
ADD COLUMN IF NOT EXISTS `email_verify_token_expires` datetime DEFAULT NULL COMMENT 'Email verification token expiration time' 
AFTER `email_verify_token`;

-- 檢查並添加 email_verified_at 字段
ALTER TABLE `users` 
ADD COLUMN IF NOT EXISTS `email_verified_at` datetime DEFAULT NULL COMMENT 'Email verification completion time' 
AFTER `email_verified`;

-- 添加索引
ALTER TABLE `users` 
ADD INDEX IF NOT EXISTS `idx_email_verify_token` (`email_verify_token`),
ADD INDEX IF NOT EXISTS `idx_email_verify_token_expires` (`email_verify_token_expires`),
ADD INDEX IF NOT EXISTS `idx_email_verified_at` (`email_verified_at`);

-- =====================================================
-- 郵件配置表 (email_settings)
-- 存儲郵件系統配置
-- =====================================================

CREATE TABLE IF NOT EXISTS `email_settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Setting ID',
    `setting_key` varchar(100) NOT NULL COMMENT 'Setting key',
    `setting_value` text DEFAULT NULL COMMENT 'Setting value',
    `description` varchar(255) DEFAULT NULL COMMENT 'Setting description',
    `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Is active',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Created time',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated time',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_setting_key` (`setting_key`),
    KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Email settings table';

-- =====================================================
-- 插入預設郵件配置
-- =====================================================

INSERT INTO `email_settings` (`setting_key`, `setting_value`, `description`) VALUES
('smtp_enabled', '0', 'Enable SMTP for email sending'),
('smtp_host', '', 'SMTP server host'),
('smtp_port', '587', 'SMTP server port'),
('smtp_username', '', 'SMTP username'),
('smtp_password', '', 'SMTP password'),
('smtp_encryption', 'tls', 'SMTP encryption (tls/ssl)'),
('from_email', '<EMAIL>', 'Default from email address'),
('from_name', 'KelvinKMS', 'Default from name'),
('verification_token_expires', '24', 'Email verification token expiration hours'),
('password_reset_token_expires', '1', 'Password reset token expiration hours'),
('enable_email_verification', '1', 'Enable email verification for new users'),
('enable_password_reset_email', '1', 'Enable password reset emails')
ON DUPLICATE KEY UPDATE 
    `setting_value` = VALUES(`setting_value`),
    `updated_at` = CURRENT_TIMESTAMP;

-- =====================================================
-- 郵件模板表 (email_templates)
-- 存儲郵件模板
-- =====================================================

CREATE TABLE IF NOT EXISTS `email_templates` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'Template ID',
    `template_key` varchar(100) NOT NULL COMMENT 'Template key',
    `language` varchar(5) NOT NULL DEFAULT 'en' COMMENT 'Language code',
    `subject` varchar(255) NOT NULL COMMENT 'Email subject',
    `content` text NOT NULL COMMENT 'Email content template',
    `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Is active',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Created time',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated time',
    
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_template_language` (`template_key`, `language`),
    KEY `idx_template_key` (`template_key`),
    KEY `idx_language` (`language`),
    KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Email templates table';

-- =====================================================
-- 插入預設郵件模板
-- =====================================================

INSERT INTO `email_templates` (`template_key`, `language`, `subject`, `content`) VALUES
-- 英文郵件驗證模板
('email_verification', 'en', 'Verify Your {{app_name}} Account', 
'Hello {{username}},\n\nThank you for registering with {{app_name}}. To complete your registration and activate your account, please click the link below to verify your email address:\n\n{{verification_url}}\n\nThis verification link will expire in {{expire_hours}} hours.\n\nIf you did not create an account, please ignore this email.\n\nBest regards,\n{{company_name}}'),

-- 中文郵件驗證模板
('email_verification', 'zh', '驗證您的 {{app_name}} 帳號', 
'您好 {{username}}，\n\n感謝您註冊 {{app_name}}。為了完成註冊並激活您的帳號，請點擊以下鏈接驗證您的郵箱地址：\n\n{{verification_url}}\n\n此驗證鏈接將在 {{expire_hours}} 小時後過期。\n\n如果您沒有創建帳號，請忽略此郵件。\n\n此致\n{{company_name}}'),

-- 英文密碼重置模板
('password_reset', 'en', 'Reset Your {{app_name}} Password', 
'Hello {{username}},\n\nWe received a request to reset your password for your {{app_name}} account. Click the link below to reset your password:\n\n{{reset_url}}\n\nThis reset link will expire in {{expire_hours}} hour(s).\n\nIf you did not request a password reset, please ignore this email.\n\nBest regards,\n{{company_name}}'),

-- 中文密碼重置模板
('password_reset', 'zh', '重置您的 {{app_name}} 密碼', 
'您好 {{username}}，\n\n我們收到了重置您 {{app_name}} 帳號密碼的請求。請點擊以下鏈接重置您的密碼：\n\n{{reset_url}}\n\n此重置鏈接將在 {{expire_hours}} 小時後過期。\n\n如果您沒有請求重置密碼，請忽略此郵件。\n\n此致\n{{company_name}}')

ON DUPLICATE KEY UPDATE 
    `subject` = VALUES(`subject`),
    `content` = VALUES(`content`),
    `updated_at` = CURRENT_TIMESTAMP;

COMMIT;
