-- 會員公司資訊系統 SQL 腳本
-- 創建會員公司資訊表格和相關索引

-- 使用正確的數據庫
USE kms_receipt_maker;

-- 創建會員公司資訊表格

CREATE TABLE IF NOT EXISTS user_company_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    company_name VARCHAR(255) NOT NULL,
    company_website VARCHAR(255),
    company_phone VARCHAR(50),
    company_email VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_company (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 創建索引以提高查詢效能
CREATE INDEX idx_user_company_user_id ON user_company_info(user_id);
CREATE INDEX idx_user_company_name ON user_company_info(company_name);