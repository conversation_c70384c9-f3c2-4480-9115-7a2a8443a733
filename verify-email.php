<?php
session_start();

// 如果已經登入，重定向到主頁
if (isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit();
}

require_once 'php/DatabaseMySQLi.php';
require_once 'php/CreditManager.php';

$token = $_GET['token'] ?? '';
$verification_result = null;
$error_message = null;
$success_message = null;

// 驗證郵件令牌
if (!empty($token)) {
    try {
        $db = new Database();
        $conn = $db->getConnection();
        
        // 查詢用戶和驗證令牌
        $stmt = $conn->prepare("
            SELECT id, username, email, email_verified, email_verify_token_expires, status 
            FROM users 
            WHERE email_verify_token = ? 
            AND email_verified = 0
        ");
        $stmt->bind_param('s', $token);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($user = $result->fetch_assoc()) {
            // 檢查令牌是否過期（24小時）
            $token_expires = $user['email_verify_token_expires'] ?? null;
            if ($token_expires && strtotime($token_expires) < time()) {
                $error_message = 'Verification link has expired. Please register again or request a new verification email.';
            } else {
                // 驗證成功，更新用戶狀態
                $stmt = $conn->prepare("
                    UPDATE users 
                    SET email_verified = 1, 
                        email_verified_at = NOW(), 
                        status = 'active',
                        email_verify_token = NULL,
                        email_verify_token_expires = NULL,
                        updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->bind_param('i', $user['id']);
                
                if ($stmt->execute()) {
                    // 試用用戶不給註冊獎勵點數，改為14天試用期
                    // 註冊獎勵點數已移除，改為試用系統
                    
                    $success_message = 'Email verification successful! Your account has been activated with a 14-day free trial. You can now log in and start using the system.';
                    $verification_result = 'success';
                } else {
                    $error_message = 'Failed to activate account. Please try again or contact support.';
                }
            }
        } else {
            $error_message = 'Invalid or expired verification link. The link may have already been used or has expired.';
        }
        
    } catch (Exception $e) {
        error_log("Email verification error: " . $e->getMessage());
        $error_message = 'System error occurred during verification. Please try again later.';
    }
} else {
    $error_message = 'No verification token provided.';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - KMS Receipt Maker</title>
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/auth.css" rel="stylesheet">
    <style>
        .verification-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
        }
        
        .verification-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .verification-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            margin-bottom: 30px;
        }
        
        .verification-icon.success {
            background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
            color: white;
        }
        
        .verification-icon.error {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }
        
        .verification-title {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }
        
        .verification-message {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
            color: #666;
        }
        
        .verification-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn-verification {
            padding: 12px 30px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4a90e2 0%, #3a7bd5 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 2px solid #e9ecef;
        }
        
        .btn-secondary:hover {
            background: #e9ecef;
            color: #495057;
            text-decoration: none;
        }
        
        .language-switcher {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .kms-lang-btn {
            background: transparent;
            border: 1px solid #dee2e6;
            color: #6c757d;
            padding: 8px 16px;
            margin: 0 5px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
        }
        
        .kms-lang-btn:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
        }
        
        @media (max-width: 768px) {
            .verification-card {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .verification-actions {
                flex-direction: column;
            }
            
            .btn-verification {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <div class="verification-card">
            <?php if ($verification_result === 'success'): ?>
                <div class="verification-icon success">✓</div>
                <h1 class="verification-title" data-lang="verification_success_title">Email Verified Successfully!</h1>
                <p class="verification-message" data-lang="verification_success_message">
                    <?php echo htmlspecialchars($success_message); ?>
                </p>
                <div class="verification-actions">
                    <a href="login.php" class="btn-verification btn-primary" data-lang="login_now">Login Now</a>
                    <a href="index.php" class="btn-verification btn-secondary" data-lang="go_home">Go to Home</a>
                </div>
            <?php else: ?>
                <div class="verification-icon error">✗</div>
                <h1 class="verification-title" data-lang="verification_failed_title">Verification Failed</h1>
                <p class="verification-message" data-lang="verification_failed_message">
                    <?php echo htmlspecialchars($error_message); ?>
                </p>
                <div class="verification-actions">
                    <a href="register.php" class="btn-verification btn-primary" data-lang="register_again">Register Again</a>
                    <a href="login.php" class="btn-verification btn-secondary" data-lang="try_login">Try Login</a>
                </div>
            <?php endif; ?>
            
            <div class="language-switcher">
                <button class="kms-lang-btn" onclick="changeLanguage('en')" data-lang-code="en">
                    <span class="flag-icon">🌐</span> English
                </button>
                <button class="kms-lang-btn" onclick="changeLanguage('zh')" data-lang-code="zh">
                    <span class="flag-icon">🌐</span> 中文
                </button>
            </div>
        </div>
    </div>
    
    <script src="js/language.js"></script>
    <script>
        // 自動重定向到登入頁面（成功驗證後5秒）
        <?php if ($verification_result === 'success'): ?>
        setTimeout(function() {
            if (confirm('Verification successful! Redirect to login page now?')) {
                window.location.href = 'login.php';
            }
        }, 5000);
        <?php endif; ?>
    </script>
</body>
</html>
