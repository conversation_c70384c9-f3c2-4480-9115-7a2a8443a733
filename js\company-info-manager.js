/**
 * 公司資訊管理器
 * 處理會員公司資訊的保存、載入和驗證
 */

class CompanyInfoManager {
    constructor() {
        this.apiEndpoint = 'php/manage_company_info.php';
        this.init();
    }

    init() {
        // 頁面載入時自動載入公司資訊
        this.loadCompanyInfo();
        
        // 綁定表單驗證事件
        this.bindValidationEvents();
    }

    /**
     * 載入公司資訊
     */
    async loadCompanyInfo() {
        try {
            const response = await fetch(this.apiEndpoint, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const result = await response.json();

            if (result.success && result.data && Object.keys(result.data).length > 0) {
                this.populateCompanyForm(result.data);
                this.showMessage('公司資訊載入成功', 'success');
            } else {
                console.log('尚未設定公司資訊');
            }
        } catch (error) {
            console.error('載入公司資訊失敗:', error);
            this.showMessage('載入公司資訊失敗', 'error');
        }
    }

    /**
     * 保存公司資訊
     */
    async saveCompanyInfo() {
        const companyData = this.getCompanyFormData();
        
        // 驗證必填欄位
        if (!this.validateCompanyData(companyData)) {
            return;
        }

        try {
            const response = await fetch(this.apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(companyData)
            });

            const result = await response.json();

            if (result.success) {
                this.showMessage('公司資訊保存成功', 'success');
                this.markFormAsValid();
            } else {
                this.showMessage(result.message || '保存失敗', 'error');
                if (result.data && Array.isArray(result.data)) {
                    result.data.forEach(error => {
                        this.showMessage(error, 'error');
                    });
                }
            }
        } catch (error) {
            console.error('保存公司資訊失敗:', error);
            this.showMessage('保存公司資訊失敗', 'error');
        }
    }

    /**
     * 清空公司資訊
     */
    clearCompanyInfo() {
        if (confirm('確定要清空所有公司資訊嗎？')) {
            document.getElementById('companyName').value = '';
            document.getElementById('companyWebsite').value = '';
            document.getElementById('companyPhone').value = '';
            document.getElementById('companyEmail').value = '';
            
            this.clearFormValidation();
            this.showMessage('公司資訊已清空', 'info');
        }
    }

    /**
     * 獲取表單數據
     */
    getCompanyFormData() {
        return {
            company_name: document.getElementById('companyName').value.trim(),
            company_website: document.getElementById('companyWebsite').value.trim(),
            company_phone: document.getElementById('companyPhone').value.trim(),
            company_email: document.getElementById('companyEmail').value.trim()
        };
    }

    /**
     * 填充表單數據
     */
    populateCompanyForm(data) {
        document.getElementById('companyName').value = data.company_name || '';
        document.getElementById('companyWebsite').value = data.company_website || '';
        document.getElementById('companyPhone').value = data.company_phone || '';
        document.getElementById('companyEmail').value = data.company_email || '';
        
        this.validateAllFields();
    }

    /**
     * 驗證公司數據
     */
    validateCompanyData(data) {
        let isValid = true;
        const errors = [];

        // 公司名稱必填
        if (!data.company_name) {
            errors.push('公司名稱不能為空');
            this.markFieldAsInvalid('companyName');
            isValid = false;
        } else {
            this.markFieldAsValid('companyName');
        }

        // 網址格式驗證
        if (data.company_website && !this.isValidUrl(data.company_website)) {
            errors.push('網址格式不正確');
            this.markFieldAsInvalid('companyWebsite');
            isValid = false;
        } else if (data.company_website) {
            this.markFieldAsValid('companyWebsite');
        }

        // 電話格式驗證
        if (data.company_phone && !this.isValidPhone(data.company_phone)) {
            errors.push('電話格式不正確');
            this.markFieldAsInvalid('companyPhone');
            isValid = false;
        } else if (data.company_phone) {
            this.markFieldAsValid('companyPhone');
        }

        // 電子郵件格式驗證
        if (data.company_email && !this.isValidEmail(data.company_email)) {
            errors.push('電子郵件格式不正確');
            this.markFieldAsInvalid('companyEmail');
            isValid = false;
        } else if (data.company_email) {
            this.markFieldAsValid('companyEmail');
        }

        if (!isValid) {
            errors.forEach(error => this.showMessage(error, 'error'));
        }

        return isValid;
    }

    /**
     * 綁定驗證事件
     */
    bindValidationEvents() {
        const fields = ['companyName', 'companyWebsite', 'companyPhone', 'companyEmail'];
        
        fields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('blur', () => this.validateField(fieldId));
                field.addEventListener('input', () => this.clearFieldValidation(fieldId));
            }
        });
    }

    /**
     * 驗證單個欄位
     */
    validateField(fieldId) {
        const field = document.getElementById(fieldId);
        const value = field.value.trim();

        switch (fieldId) {
            case 'companyName':
                if (!value) {
                    this.markFieldAsInvalid(fieldId);
                } else {
                    this.markFieldAsValid(fieldId);
                }
                break;
            case 'companyWebsite':
                if (value && !this.isValidUrl(value)) {
                    this.markFieldAsInvalid(fieldId);
                } else if (value) {
                    this.markFieldAsValid(fieldId);
                }
                break;
            case 'companyPhone':
                if (value && !this.isValidPhone(value)) {
                    this.markFieldAsInvalid(fieldId);
                } else if (value) {
                    this.markFieldAsValid(fieldId);
                }
                break;
            case 'companyEmail':
                if (value && !this.isValidEmail(value)) {
                    this.markFieldAsInvalid(fieldId);
                } else if (value) {
                    this.markFieldAsValid(fieldId);
                }
                break;
        }
    }

    /**
     * 驗證所有欄位
     */
    validateAllFields() {
        const fields = ['companyName', 'companyWebsite', 'companyPhone', 'companyEmail'];
        fields.forEach(fieldId => this.validateField(fieldId));
    }

    /**
     * 標記欄位為有效
     */
    markFieldAsValid(fieldId) {
        const field = document.getElementById(fieldId);
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
    }

    /**
     * 標記欄位為無效
     */
    markFieldAsInvalid(fieldId) {
        const field = document.getElementById(fieldId);
        field.classList.remove('is-valid');
        field.classList.add('is-invalid');
    }

    /**
     * 清除欄位驗證狀態
     */
    clearFieldValidation(fieldId) {
        const field = document.getElementById(fieldId);
        field.classList.remove('is-valid', 'is-invalid');
    }

    /**
     * 標記整個表單為有效
     */
    markFormAsValid() {
        this.validateAllFields();
    }

    /**
     * 清除表單驗證
     */
    clearFormValidation() {
        const fields = ['companyName', 'companyWebsite', 'companyPhone', 'companyEmail'];
        fields.forEach(fieldId => this.clearFieldValidation(fieldId));
    }

    /**
     * 驗證 URL 格式
     */
    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * 驗證電話格式
     */
    isValidPhone(phone) {
        const phoneRegex = /^[\d\-\+\(\)\s]+$/;
        return phoneRegex.test(phone);
    }

    /**
     * 驗證電子郵件格式
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * 顯示訊息
     */
    showMessage(message, type = 'info') {
        // 使用現有的訊息系統
        if (typeof showMessage === 'function') {
            showMessage(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    /**
     * 獲取公司資訊用於收據預覽
     */
    getCompanyInfoForReceipt() {
        const data = this.getCompanyFormData();
        return {
            name: data.company_name,
            website: data.company_website,
            phone: data.company_phone,
            email: data.company_email
        };
    }
}

// 全域函數供 HTML 調用
let companyInfoManager;

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    companyInfoManager = new CompanyInfoManager();
    // 確保在全域範圍內可用
    window.companyInfoManager = companyInfoManager;
});

// 全域函數
function loadCompanyInfo() {
    if (companyInfoManager) {
        companyInfoManager.loadCompanyInfo();
    }
}

function saveCompanyInfo() {
    if (companyInfoManager) {
        companyInfoManager.saveCompanyInfo();
    }
}

function clearCompanyInfo() {
    if (companyInfoManager) {
        companyInfoManager.clearCompanyInfo();
    }
}

// 導出給其他模組使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CompanyInfoManager;
}