/* 公司資訊區域樣式 */
:root {
  --company-primary-color: var(--color-3);
  --company-border-color: var(--border-color-3);
  --company-text-color: var(--text-color-1);
  --company-bg-color: rgba(47, 255, 92, 0.05);
  --company-hover-color: rgba(47, 255, 92, 0.1);
}

/* 基礎樣式 */
.company-info-section {
  background: var(--company-bg-color);
  border: 2px solid var(--company-border-color);
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 1rem;
  transition: all 0.3s ease;
}

.company-info-section:hover {
  background: var(--company-hover-color);
  box-shadow: 0 4px 12px rgba(47, 255, 92, 0.2);
}

.company-info-section h3 {
  color: var(--company-primary-color);
  font-weight: 600;
  margin-bottom: 0;
  border-bottom: 2px solid var(--company-border-color) !important;
}

/* 布局樣式 */
.company-info-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.company-info-actions .btn {
  border-radius: 8px;
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
  transition: all 0.3s ease;
}

.company-info-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 表單樣式 */
.company-info-section .form-control {
  border: 2px solid var(--company-border-color);
  border-radius: 8px;
  padding: 0.75rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

.company-info-section .form-control:focus {
  border-color: var(--company-primary-color);
  box-shadow: 0 0 0 0.2rem rgba(47, 255, 92, 0.25);
  background: rgba(255, 255, 255, 1);
}

.company-info-section .form-control:valid {
  border-color: var(--company-primary-color);
}

.company-info-section .form-control[required]:invalid {
  border-color: var(--color-4);
}

.company-info-section .form-label {
  color: var(--company-text-color);
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.company-info-section .form-label[data-lang="company_name"]::after {
  content: " *";
  color: var(--color-4);
  font-weight: bold;
}

/* 交互樣式 */
.company-info-section .btn-outline-primary {
  border-color: var(--company-border-color);
  color: var(--company-primary-color);
}

.company-info-section .btn-outline-primary:hover {
  background-color: var(--company-primary-color);
  border-color: var(--company-primary-color);
  color: var(--text-color-2);
}

.company-info-section .btn-outline-success {
  border-color: var(--company-border-color);
  color: var(--company-primary-color);
}

.company-info-section .btn-outline-success:hover {
  background-color: var(--company-primary-color);
  border-color: var(--company-primary-color);
  color: var(--text-color-2);
}

.company-info-section .btn-outline-secondary {
  border-color: var(--border-color-1);
  color: var(--text-color-1);
}

.company-info-section .btn-outline-secondary:hover {
  background-color: var(--color-1);
  border-color: var(--color-1);
  color: var(--text-color-2);
}

/* 響應式樣式 */
@media (max-width: 768px) {
  .company-info-section {
    padding: 1rem;
    margin-top: 0.5rem;
  }
  
  .company-info-actions {
    justify-content: center;
    margin-top: 1rem;
  }
  
  .company-info-actions .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
  }
  
  .company-info-section h3 {
    font-size: 1.1rem;
    text-align: center;
    margin-bottom: 1rem;
  }
}

@media (max-width: 576px) {
  .company-info-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .company-info-actions .btn {
    width: 100%;
    margin-bottom: 0.25rem;
  }
}

/* 動畫效果 */
.company-info-section {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 成功/錯誤狀態樣式 */
.company-info-section .form-control.is-valid {
  border-color: var(--company-primary-color);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2330ff5c' d='m2.3 6.73.94-.94 2.94 2.94L8.5 6.4l.94.94L6.5 10.27z'/%3e%3c/svg%3e");
}

.company-info-section .form-control.is-invalid {
  border-color: var(--color-4);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23ff3131'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.5 5.5 1 1m0-1-1 1'/%3e%3c/svg%3e");
}