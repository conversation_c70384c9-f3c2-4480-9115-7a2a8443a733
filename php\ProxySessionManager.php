<?php
/**
 * 代理會話管理器
 * KMS Receipt Maker - Proxy Session Manager
 * 
 * 管理管理員代理登入會話的安全性和有效性
 */

class ProxySessionManager {
    private $db;
    private $conn;
    
    public function __construct() {
        require_once 'DatabaseMySQLi.php';
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }
    
    /**
     * 檢查代理會話是否有效
     */
    public function isValidProxySession() {
        if (!isset($_SESSION['is_proxy_session']) || !$_SESSION['is_proxy_session']) {
            return false;
        }
        
        if (!isset($_SESSION['original_admin_id']) || !isset($_SESSION['proxy_start_time'])) {
            return false;
        }
        
        // 檢查會話是否過期（預設1小時）
        $maxDuration = $this->getProxySetting('max_proxy_session_duration', 3600);
        if ((time() - $_SESSION['proxy_start_time']) > $maxDuration) {
            $this->logProxyAction($_SESSION['original_admin_id'], $_SESSION['user_id'], 'session_expired');
            $this->endProxySession();
            return false;
        }
        
        // 檢查原始管理員是否仍然存在且為管理員
        $stmt = $this->conn->prepare("SELECT id, user_type, status FROM users WHERE id = ? AND user_type = 'admin' AND status = 'active'");
        $stmt->bind_param('i', $_SESSION['original_admin_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            $this->logProxyAction($_SESSION['original_admin_id'], $_SESSION['user_id'], 'admin_invalid');
            $this->endProxySession();
            return false;
        }
        
        return true;
    }
    
    /**
     * 檢查用戶是否可以被代理登入
     */
    public function canProxyLogin($userId) {
        $stmt = $this->conn->prepare("
            SELECT user_type, status, email_verified 
            FROM users 
            WHERE id = ?
        ");
        $stmt->bind_param('i', $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($user = $result->fetch_assoc()) {
            // 不能代理登入管理員帳號
            if ($user['user_type'] === 'admin') {
                return false;
            }
            
            // 只能代理登入活躍用戶
            if ($user['status'] !== 'active') {
                return false;
            }
            
            // 檢查允許的用戶類型
            $allowedTypes = explode(',', $this->getProxySetting('allowed_proxy_user_types', 'member'));
            if (!in_array($user['user_type'], $allowedTypes)) {
                return false;
            }
            
            return true;
        }
        
        return false;
    }
    
    /**
     * 記錄代理操作
     */
    public function logProxyAction($adminId, $targetUserId, $action, $additionalData = null) {
        $stmt = $this->conn->prepare("
            INSERT INTO admin_proxy_logs (admin_id, target_user_id, action, ip_address, user_agent, additional_data, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        $additionalDataJson = $additionalData ? json_encode($additionalData) : null;
        
        $stmt->bind_param('iissss', $adminId, $targetUserId, $action, $ipAddress, $userAgent, $additionalDataJson);
        return $stmt->execute();
    }
    
    /**
     * 結束代理會話
     */
    public function endProxySession() {
        if (isset($_SESSION['is_proxy_session']) && $_SESSION['is_proxy_session']) {
            // 記錄會話結束
            if (isset($_SESSION['original_admin_id']) && isset($_SESSION['user_id'])) {
                $this->logProxyAction($_SESSION['original_admin_id'], $_SESSION['user_id'], 'session_ended');
            }
            
            // 清除代理會話數據
            unset($_SESSION['original_admin_id']);
            unset($_SESSION['original_admin_username']);
            unset($_SESSION['original_admin_type']);
            unset($_SESSION['is_proxy_session']);
            unset($_SESSION['proxy_start_time']);
        }
    }
    
    /**
     * 獲取代理設置
     */
    public function getProxySetting($key, $default = null) {
        $stmt = $this->conn->prepare("SELECT setting_value FROM admin_proxy_settings WHERE setting_key = ? AND is_active = 1");
        $stmt->bind_param('s', $key);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            return $row['setting_value'];
        }
        
        return $default;
    }
    
    /**
     * 檢查代理功能是否啟用
     */
    public function isProxyLoginEnabled() {
        return $this->getProxySetting('enable_proxy_login', '1') === '1';
    }
    
    /**
     * 獲取代理會話統計
     */
    public function getProxySessionStats($adminId = null) {
        $sql = "
            SELECT 
                COUNT(*) as total_sessions,
                COUNT(DISTINCT target_user_id) as unique_users,
                MAX(created_at) as last_session,
                AVG(session_duration) as avg_duration
            FROM admin_proxy_logs 
            WHERE action = 'proxy_login'
        ";
        
        if ($adminId) {
            $sql .= " AND admin_id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param('i', $adminId);
        } else {
            $stmt = $this->conn->prepare($sql);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        return $result->fetch_assoc();
    }
    
    /**
     * 獲取當前代理會話信息
     */
    public function getCurrentProxyInfo() {
        if (!$this->isValidProxySession()) {
            return null;
        }
        
        return [
            'is_proxy_session' => true,
            'original_admin_id' => $_SESSION['original_admin_id'],
            'original_admin_username' => $_SESSION['original_admin_username'],
            'current_user_id' => $_SESSION['user_id'],
            'current_username' => $_SESSION['username'],
            'proxy_start_time' => $_SESSION['proxy_start_time'],
            'proxy_duration' => time() - $_SESSION['proxy_start_time'],
            'max_duration' => $this->getProxySetting('max_proxy_session_duration', 3600)
        ];
    }
    
    /**
     * 檢查是否需要顯示會話警告
     */
    public function shouldShowSessionWarning() {
        if (!$this->isValidProxySession()) {
            return false;
        }
        
        $warningTime = $this->getProxySetting('proxy_session_warning_time', 300); // 5分鐘
        $maxDuration = $this->getProxySetting('max_proxy_session_duration', 3600);
        $currentDuration = time() - $_SESSION['proxy_start_time'];
        
        return ($maxDuration - $currentDuration) <= $warningTime;
    }
    
    /**
     * 延長代理會話
     */
    public function extendProxySession($minutes = 30) {
        if (!$this->isValidProxySession()) {
            return false;
        }
        
        $_SESSION['proxy_start_time'] = time() - (30 * 60); // 延長30分鐘
        
        $this->logProxyAction(
            $_SESSION['original_admin_id'], 
            $_SESSION['user_id'], 
            'session_extended',
            ['extended_minutes' => $minutes]
        );
        
        return true;
    }
}
?>
