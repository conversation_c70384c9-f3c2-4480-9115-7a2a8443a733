<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'config.php';

// 只接受POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// 獲取POST數據
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['email'])) {
    echo json_encode(['error' => 'Email is required']);
    exit;
}

$email = trim($input['email']);

// 驗證Email格式
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    echo json_encode([
        'valid' => false,
        'message' => 'Invalid email format'
    ]);
    exit;
}

// 檢查Email長度
if (strlen($email) > 100) {
    echo json_encode([
        'valid' => false,
        'message' => 'Email address is too long'
    ]);
    exit;
}

try {
    // 連接數據庫
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 檢查Email是否已存在
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
    $stmt->execute([$email]);
    $count = $stmt->fetchColumn();
    
    if ($count > 0) {
        echo json_encode([
            'valid' => false,
            'available' => false,
            'message' => 'Email address is already registered'
        ]);
    } else {
        echo json_encode([
            'valid' => true,
            'available' => true,
            'message' => 'Email is valid and available'
        ]);
    }
    
} catch (PDOException $e) {
    error_log("Database error in check_email.php: " . $e->getMessage());
    echo json_encode([
        'error' => 'Database connection failed'
    ]);
}
?>