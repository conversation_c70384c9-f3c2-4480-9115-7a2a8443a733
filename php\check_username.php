<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'config.php';

// 只接受POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// 獲取POST數據
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['username'])) {
    echo json_encode(['error' => 'Username is required']);
    exit;
}

$username = trim($input['username']);

// 驗證用戶名格式：只允許英文字母、數字和下劃線
if (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
    echo json_encode([
        'available' => false,
        'message' => 'Username can only contain letters, numbers, and underscores'
    ]);
    exit;
}

// 檢查用戶名長度
if (strlen($username) < 3 || strlen($username) > 20) {
    echo json_encode([
        'available' => false,
        'message' => 'Username must be between 3 and 20 characters'
    ]);
    exit;
}

try {
    // 連接數據庫
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 檢查用戶名是否已存在
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
    $stmt->execute([$username]);
    $count = $stmt->fetchColumn();
    
    if ($count > 0) {
        echo json_encode([
            'available' => false,
            'message' => 'Username is already taken'
        ]);
    } else {
        echo json_encode([
            'available' => true,
            'message' => 'Username is available'
        ]);
    }
    
} catch (PDOException $e) {
    error_log("Database error in check_username.php: " . $e->getMessage());
    echo json_encode([
        'error' => 'Database connection failed'
    ]);
}
?>