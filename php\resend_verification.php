<?php
/**
 * 重新發送郵件驗證
 * KMS Receipt Maker - Resend Email Verification
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'config.php';
require_once 'DatabaseMySQLi.php';
require_once 'EmailService.php';
require_once 'Response.php';

// 只接受POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    Response::errorAndExit();
}

// 獲取POST數據
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['email'])) {
    Response::errorAndExit();
}

$email = trim($input['email']);
$language = $input['language'] ?? 'en';

// 驗證Email格式
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    Response::errorAndExit();
}

try {
    $db = new Database();
    $conn = $db->getConnection();
    
    // 查詢用戶
    $stmt = $conn->prepare("
        SELECT id, username, email, email_verified, email_verify_token_expires, status 
        FROM users 
        WHERE email = ? 
        AND email_verified = 0
    ");
    $stmt->bind_param('s', $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($user = $result->fetch_assoc()) {
        // 檢查是否在冷卻期內（防止濫用，5分鐘內只能發送一次）
        $last_sent_check = $conn->prepare("
            SELECT sent_at 
            FROM email_logs 
            WHERE recipient = ? 
            AND email_type = 'verification' 
            AND success = 1 
            ORDER BY sent_at DESC 
            LIMIT 1
        ");
        $last_sent_check->bind_param('s', $email);
        $last_sent_check->execute();
        $last_sent_result = $last_sent_check->get_result();
        
        if ($last_sent = $last_sent_result->fetch_assoc()) {
            $time_diff = time() - strtotime($last_sent['sent_at']);
            if ($time_diff < 300) { // 5分鐘 = 300秒
                $remaining_time = 300 - $time_diff;
                Response::errorAndExit();
            }
        }
        
        // 生成新的驗證令牌
        $new_token = bin2hex(random_bytes(32));
        $token_expires = date('Y-m-d H:i:s', time() + 24 * 60 * 60); // 24小時後過期
        
        // 更新用戶的驗證令牌
        $stmt = $conn->prepare("
            UPDATE users 
            SET email_verify_token = ?, 
                email_verify_token_expires = ?,
                updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->bind_param('ssi', $new_token, $token_expires, $user['id']);
        
        if ($stmt->execute()) {
            // 發送驗證郵件
            $emailService = new EmailService();
            $email_sent = $emailService->sendEmailVerification($email, $user['username'], $new_token, $language);
            
            if ($email_sent) {
                // 記錄郵件類型
                $log_stmt = $conn->prepare("
                    UPDATE email_logs 
                    SET email_type = 'verification', user_id = ? 
                    WHERE recipient = ? 
                    ORDER BY created_at DESC 
                    LIMIT 1
                ");
                $log_stmt->bind_param('is', $user['id'], $email);
                $log_stmt->execute();
                
                Response::successAndExit(null, 'Verification email has been resent successfully. Please check your email.');
            } else {
                Response::errorAndExit('Failed to send verification email. Please try again later.');
            }
        } else {
            Response::errorAndExit('Failed to update verification token. Please try again later.');
        }
        
    } else {
        // 為了安全，即使郵箱不存在或已驗證也顯示相同消息
        Response::successAndExit(null, 'If the email address exists and is not verified, a verification email has been sent.');
    }
    
} catch (Exception $e) {
    error_log("Resend verification error: " . $e->getMessage());
    Response::errorAndExit('System error occurred. Please try again later.');
}
?>
