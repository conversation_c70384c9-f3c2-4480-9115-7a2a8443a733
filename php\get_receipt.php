<?php
/**
 * 獲取單個收據詳情
 * KMS PC Receipt Maker
 */

session_start();

require_once 'ReceiptManager.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

// 檢查用戶是否已登入
if (!isset($_SESSION['user_id'])) {
    Response::errorAndExit('請先登入', 401);
}

$userId = $_SESSION['user_id'];

try {
    // 獲取收據ID
    $receiptId = isset($_GET['id']) ? intval($_GET['id']) : 0;
    
    if ($receiptId <= 0) {
        Response::errorAndExit('無效的收據ID', 400);
    }
    
    $receiptManager = new ReceiptManager();
    $receipt = $receiptManager->getReceiptById($receiptId, $userId);
    
    if (!$receipt) {
        Response::errorAndExit('收據不存在', 404);
    }
    
    Response::successAndExit($receipt, '收據詳情獲取成功');
    
} catch (Exception $e) {
    error_log('Get receipt error: ' . $e->getMessage());
    Response::errorAndExit('獲取收據詳情失敗: ' . $e->getMessage());
}
?>