<?php
/**
 * 刪除電腦零件
 * KMS PC Receipt Maker
 */

session_start();

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

// 檢查用戶是否已登入
if (!isset($_SESSION['user_id'])) {
    Response::errorAndExit('請先登入', 401);
}

$currentUserId = $_SESSION['user_id'];

// 只允許POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Response::errorAndExit('只允許POST請求', 405);
}

try {
    // 獲取JSON數據
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data || !isset($data['id'])) {
        Response::errorAndExit('無效的請求數據');
    }
    
    $partId = intval($data['id']);
    
    if ($partId <= 0) {
        Response::errorAndExit('無效的零件ID', 400);
    }
    
    $db = new Database();
    
    // 檢查零件是否存在且屬於當前用戶或管理員
    $checkSql = "SELECT id, created_by FROM pc_parts WHERE id = ?";
    $existing = $db->fetch($checkSql, [$partId]);
    
    if (!$existing) {
        Response::errorAndExit('零件不存在', 404);
    }
    
    // 檢查權限：只有項目創建者可以刪除（包括管理員也只能刪除自己的項目）
    if ($existing['created_by'] != $currentUserId) {
        Response::errorAndExit('沒有權限刪除此項目', 403);
    }
    
    // 刪除零件
    $sql = "DELETE FROM pc_parts WHERE id = ?";
    $affectedRows = $db->delete($sql, [$partId]);
    
    if ($affectedRows > 0) {
        Response::successAndExit(['id' => $partId], '零件刪除成功');
    } else {
        Response::errorAndExit('刪除零件失敗');
    }
    
} catch (Exception $e) {
    error_log('Delete PC part error: ' . $e->getMessage());
    Response::errorAndExit('刪除零件失敗: ' . $e->getMessage());
}
?>