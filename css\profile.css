/* =====================================================
   個人資料頁面樣式 - profile.css
   ===================================================== */

/* 基礎樣式 */
:root {
  --color-1: rgb(0, 200, 255);
  --color-2: rgb(255, 200, 0);
  --color-3: rgb(47, 255, 92);
  --color-4: rgb(255, 49, 49);
  --color-5: rgb(0, 255, 234);
  --border-color-1: rgb(0, 162, 255);
  --border-color-2: rgb(255, 180, 19);
  --border-color-3: rgb(30, 255, 0);
  --border-color-4: rgb(255, 63, 63);
  --border-color-5: rgb(12, 255, 243);
  --text-color-1: rgb(0, 0, 0);
  --text-color-2: rgb(255, 255, 255);
  --text-color-3: rgb(0, 255, 128);
  --text-color-4: rgb(255, 128, 0);
  --text-color-5: rgb(255, 0, 128);
  --profile-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  --card-shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.profile-body {
  background: var(--profile-bg);
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 0;
}

/* 布局樣式 */
.profile-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.profile-navbar {
  background: rgb(255, 255, 255);
  box-shadow: var(--card-shadow);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 3px solid var(--color-1);
}

.profile-nav-brand {
  display: flex;
  align-items: center;
  gap: 15px;
}

.profile-icon {
  font-size: 2rem;
  background: linear-gradient(45deg, var(--color-1), var(--color-5));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.profile-nav-brand h1 {
  margin: 0;
  color: var(--text-color-1);
  font-size: 1.8rem;
  font-weight: 700;
}

.profile-nav-actions {
  display: flex;
  gap: 10px;
}

.profile-content {
  flex: 1;
  padding: 1rem;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

/* 居中容器樣式 */
.profile-sections-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

/* 卡片樣式 */
.profile-card {
  background: rgb(255 210 60);
  border-radius: 16px;
  box-shadow: var(--card-shadow);
  margin-bottom: 1rem;
  overflow: hidden;
  transition: all 0.3s ease;
  width: 100%;
  max-width: 600px;
}

.profile-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--card-shadow-hover);
}

.card-header {
  background: linear-gradient(45deg, var(--color-1), var(--color-5));
  color: var(--text-color-2);
  padding: 1rem 1.5rem;
  border-bottom: none;
}

.card-header h4 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.card-body {
  padding: 1.5rem;
}

/* 用戶信息卡片樣式 */
.user-info-card {
  text-align: center;
  padding: 1.5rem;
}

.user-avatar {
  margin-bottom: 1rem;
}

.avatar-icon {
  font-size: 3.5rem;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--color-1), var(--color-5));
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: var(--card-shadow);
}

.user-details h3 {
  color: var(--text-color-1);
  font-size: 1.4rem;
  font-weight: 700;
  margin-bottom: 0.3rem;
}

.user-username {
  color: #000000;
  font-size: 20px;
  margin-bottom: 0.3rem;
  font-weight: 500;
}

.user-email {
  color: #000000;
  font-size: 20px;
  margin-bottom: 0.3rem;
  font-weight: 500;
}

.user-type-badge {
  display: inline-block;
  padding: 0.4rem 0.8rem;
  border-radius: 16px;
  font-size: 20px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 1rem;
}

.user-type-badge.admin {
  background: linear-gradient(45deg, var(--color-4), var(--color-5));
  color: var(--text-color-2);
}

.user-type-badge.member {
  background: linear-gradient(45deg, var(--color-1), var(--color-3));
  color: var(--text-color-2);
}

.user-stats {
  border-top: 1px solid #e9ecef;
  padding-top: 1rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f8f9fa;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  color: #000000;
  font-size: 18px;
  font-weight: 500;
  padding-bottom: 14px;
}

.stat-value {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  padding-bottom: 14px;
}

/* 表單樣式 */
.profile-form {
  margin: 0;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  color: var(--text-color-1);
  font-weight: 600;
  margin-bottom: 0.3rem;
  display: block;
}

.form-control {
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  padding: 0.6rem 0.8rem;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
}

.form-control:focus {
  border-color: var(--color-1);
  box-shadow: 0 0 0 0.2rem rgba(0, 200, 255, 0.25);
  background-color: var(--text-color-2);
  outline: none;
}

.form-control:hover {
  border-color: var(--border-color-1);
}

.form-text {
  color: #666;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* 交互樣式 */
.btn {
  border: none;
  border-radius: 10px;
  padding: 0.6rem 1.2rem;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-primary {
  background: linear-gradient(45deg, var(--color-1), var(--color-5));
  color: var(--text-color-2);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 200, 255, 0.3);
  background: linear-gradient(45deg, var(--color-5), var(--color-1));
}

.btn-warning {
  background: linear-gradient(45deg, var(--color-1), var(--color-5));
  color: var(--text-color-2);
}

.btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 200, 255, 0.3);
  background: linear-gradient(45deg, var(--color-5), var(--color-1));
}

.btn-outline-primary {
  background: #00d4ff;
  color: #ffffff;
  border: 2px solid var(--color-1);
}

.btn-outline-primary:hover {
  background: #00f7ff;
  color: #ffffff;
  transform: translateY(-2px);
}

.btn-outline-danger {
  background: #00d4ff;
  color: #ffffff;
  border: 2px solid var(--color-1);
}

.btn-outline-danger:hover {
  background: #00f7ff;
  color: #ffffff;
  transform: translateY(-2px);
}

/* 警告消息樣式 */
.alert {
  border-radius: 12px;
  border: none;
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
}

.alert-success {
  background: linear-gradient(45deg, rgba(47, 255, 92, 0.1), rgba(40, 167, 69, 0.1));
  color: #155724;
  border-left: 4px solid var(--color-3);
}

.alert-danger {
  background: linear-gradient(45deg, rgba(255, 49, 49, 0.1), rgba(220, 53, 69, 0.1));
  color: #721c24;
  border-left: 4px solid var(--color-4);
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.btn-close:hover {
  opacity: 1;
}

/* 響應式樣式 */
@media (max-width: 768px) {
  .profile-navbar {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }
  
  .profile-nav-brand h1 {
    font-size: 1.5rem;
  }
  
  .profile-content {
    padding: 1rem;
  }
  
  .card-body {
    padding: 1.5rem;
  }
  
  .user-info-card {
    padding: 1.5rem;
  }
  
  .avatar-icon {
    width: 100px;
    height: 100px;
    font-size: 3rem;
  }
  
  .user-details h3 {
    font-size: 1.3rem;
  }
  
  .btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .profile-icon {
    font-size: 1.5rem;
  }
  
  .profile-nav-brand h1 {
    font-size: 1.3rem;
  }
  
  .card-header {
    padding: 1rem 1.5rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .user-info-card {
    padding: 1rem;
  }
  
  .avatar-icon {
    width: 80px;
    height: 80px;
    font-size: 2.5rem;
  }
  
  .user-details h3 {
    font-size: 1.2rem;
  }
  
  .form-control {
    padding: 0.6rem 0.8rem;
  }
}

/* 動畫效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-card {
  animation: fadeInUp 0.6s ease-out;
}

.profile-card:nth-child(2) {
  animation-delay: 0.1s;
}

.profile-card:nth-child(3) {
  animation-delay: 0.2s;
}

/* 表單驗證樣式 */
.form-control.is-invalid {
  border-color: var(--color-4);
  box-shadow: 0 0 0 0.2rem rgba(255, 49, 49, 0.25);
}

.form-control.is-valid {
  border-color: var(--color-3);
  box-shadow: 0 0 0 0.2rem rgba(47, 255, 92, 0.25);
}

.invalid-feedback {
  display: block;
  color: var(--color-4);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.valid-feedback {
  display: block;
  color: var(--color-3);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* 載入動畫 */
.loading {
  position: relative;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid var(--text-color-2);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 密碼強度指示器 */
.password-strength {
  margin-top: 0.5rem;
}

.strength-bar {
  height: 4px;
  border-radius: 2px;
  background: #e9ecef;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-weak {
  width: 33%;
  background: var(--color-4);
}

.strength-medium {
  width: 66%;
  background: var(--color-2);
}

.strength-strong {
  width: 100%;
  background: var(--color-3);
}

.strength-text {
  font-size: 0.75rem;
  margin-top: 0.25rem;
  font-weight: 500;
}

.strength-text.weak {
  color: var(--color-4);
}

.strength-text.medium {
  color: var(--color-2);
}

.strength-text.strong {
  color: var(--color-3);
}