<?php
/**
 * 記錄用戶活動
 * KMS PC Receipt Maker
 */

session_start();

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

// 只允許POST請求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Response::errorAndExit('只允許POST請求', 405);
}

// 檢查用戶是否已登入
if (!isset($_SESSION['user_id'])) {
    Response::errorAndExit('請先登入', 401);
}

$userId = $_SESSION['user_id'];

try {
    // 獲取JSON數據
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        Response::errorAndExit('無效的JSON數據', 400);
    }
    
    // 驗證必需字段
    $requiredFields = ['activity_type', 'description'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty(trim($input[$field]))) {
            Response::errorAndExit("缺少必需字段: {$field}", 400);
        }
    }
    
    $activityType = trim($input['activity_type']);
    $description = trim($input['description']);
    $activityCategory = trim($input['activity_category'] ?? 'general');
    $details = isset($input['details']) ? json_encode($input['details']) : null;
    $targetType = trim($input['target_type'] ?? '');
    $targetId = isset($input['target_id']) ? intval($input['target_id']) : null;
    $result = trim($input['result'] ?? 'success');
    $errorMessage = trim($input['error_message'] ?? '');
    
    // 獲取請求信息
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    $referer = $_SERVER['HTTP_REFERER'] ?? null;
    $requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'unknown';
    $requestUri = $_SERVER['REQUEST_URI'] ?? 'unknown';
    $sessionId = session_id();
    
    // 計算響應時間（如果提供）
    $responseTime = isset($input['response_time']) ? floatval($input['response_time']) : null;
    
    $db = new Database();
    
    $sql = "
        INSERT INTO user_activities (
            user_id, session_id, activity_type, activity_category, description, details,
            target_type, target_id, result, error_message,
            ip_address, user_agent, referer, request_method, request_uri, response_time
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ";
    
    $activityId = $db->insert($sql, [
        $userId, $sessionId, $activityType, $activityCategory, $description, $details,
        $targetType ?: null, $targetId, $result, $errorMessage ?: null,
        $ipAddress, $userAgent, $referer, $requestMethod, $requestUri, $responseTime
    ]);
    
    Response::successAndExit(['activity_id' => $activityId], '活動記錄成功');
    
} catch (Exception $e) {
    error_log('Log activity error: ' . $e->getMessage());
    Response::errorAndExit('活動記錄失敗: ' . $e->getMessage());
}
?>