<?php
session_start();

// 如果已經登入，重定向到主頁
if (isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit();
}

// 處理註冊表單提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    require_once 'php/config.php';
    require_once 'php/DatabaseMySQLi.php';
    
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $first_name = trim($_POST['first_name'] ?? '');
    $last_name = trim($_POST['last_name'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    
    $errors = [];
    
    // 驗證輸入
    if (empty($username)) {
        $errors[] = 'Please enter username';
    } elseif (strlen($username) < 6 || strlen($username) > 20) {
        $errors[] = 'Username must be between 6 and 20 characters';
    } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
        $errors[] = 'Username can only contain letters, numbers, and underscores';
    }
    
    if (empty($email)) {
        $errors[] = 'Please enter email address';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address';
    } elseif (strlen($email) > 100) {
        $errors[] = 'Email address is too long';
    }
    
    if (empty($password)) {
        $errors[] = 'Please enter password';
    } else {
        // 密碼強度驗證
        $password_errors = [];
        
        if (strlen($password) < 8) {
            $password_errors[] = 'Password must be at least 8 characters';
        }
        
        if (!preg_match('/[A-Z]/', $password)) {
            $password_errors[] = 'Password must contain at least one uppercase letter';
        }
        
        if (!preg_match('/[0-9]/', $password)) {
            $password_errors[] = 'Password must contain at least one number';
        }
        
        if (!preg_match('/[!@#$%^&*(),.?":{}|<>]/', $password)) {
            $password_errors[] = 'Password must contain at least one special character (!@#$%^&*)';
        }
        
        if (!empty($password_errors)) {
            $errors = array_merge($errors, $password_errors);
        }
    }
    
    if ($password !== $confirm_password) {
        $errors[] = 'Passwords do not match';
    }
    
    if (empty($first_name)) {
        $errors[] = 'Please enter first name';
    }
    
    if (empty($last_name)) {
        $errors[] = 'Please enter last name';
    } elseif (!preg_match('/^[a-zA-Z\s\-\']+$/', $last_name)) {
        $errors[] = 'Last name can only contain letters, spaces, hyphens, and apostrophes';
    } elseif (strlen($last_name) > 50) {
        $errors[] = 'Last name is too long';
    }
    
    if (!empty($first_name) && (!preg_match('/^[a-zA-Z\s\-\']+$/', $first_name) || strlen($first_name) > 50)) {
        $errors[] = 'First name can only contain letters, spaces, hyphens, and apostrophes';
    }
    
    if (!empty($phone)) {
        $phone_digits = preg_replace('/\D/', '', $phone);
        if (!preg_match('/^[0-9\s\-\(\)\+]+$/', $phone)) {
            $errors[] = 'Phone number can only contain numbers, spaces, hyphens, parentheses, and plus sign';
        } elseif (strlen($phone_digits) < 8 || strlen($phone_digits) > 15) {
            $errors[] = 'Phone number must contain 8-15 digits';
        }
    }
    
    if (empty($errors)) {
        try {
            $db = new Database();
            $conn = $db->getConnection();
            
            // 檢查用戶名和郵箱是否已存在
            $stmt = $conn->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
            $stmt->bind_param('ss', $username, $email);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $errors[] = 'Username or email already exists';
            } else {
                // 創建新用戶
                $password_hash = password_hash($password, PASSWORD_DEFAULT);
                $email_verify_token = bin2hex(random_bytes(32));
                $email_verify_token_expires = date('Y-m-d H:i:s', time() + (24 * 60 * 60)); // 24小時後過期

                // 註冊為試用用戶，不給初始點數（試用系統觸發器會自動設置試用期）
                $stmt = $conn->prepare("
                    INSERT INTO users (username, email, password, first_name, last_name, phone,
                                     user_type, status, credits, email_verify_token, email_verify_token_expires)
                    VALUES (?, ?, ?, ?, ?, ?, 'member', 'inactive', 0, ?, ?)
                ");
                $stmt->bind_param('ssssssss', $username, $email, $password_hash, $first_name, $last_name, $phone, $email_verify_token, $email_verify_token_expires);

                if ($stmt->execute()) {
                    $user_id = $conn->insert_id;

                    // 發送郵件驗證
                    try {
                        require_once 'php/EmailService.php';
                        $emailService = new EmailService();

                        // 檢測用戶語言偏好（可以從瀏覽器或用戶選擇獲取）
                        $user_language = 'en'; // 默認英文，可以根據需要調整
                        if (isset($_POST['language']) && $_POST['language'] === 'zh') {
                            $user_language = 'zh';
                        }

                        $email_sent = $emailService->sendEmailVerification($email, $username, $email_verify_token, $user_language);

                        if ($email_sent) {
                            $success = 'Registration successful! A verification email has been sent to your email address. Please check your email and click the verification link to activate your account.';
                        } else {
                            $success = 'Registration successful! However, we could not send the verification email. Please contact support for manual activation.';
                            error_log("Failed to send verification email to: $email");
                        }

                    } catch (Exception $emailError) {
                        error_log("Email sending failed for user $user_id: " . $emailError->getMessage());
                        $success = 'Registration successful! However, we could not send the verification email. Please contact support for manual activation.';
                    }

                    // 註冊成功後不立即分配點數，等郵件驗證後再分配
                    // 點數將在 verify-email.php 中分配

                } else {
                    $errors[] = 'Registration failed. Please try again later.';
                }
            }
        } catch (Exception $e) {
            error_log("Registration error: " . $e->getMessage());
            $errors[] = 'System error. Please try again later.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>註冊 - KMS Receipt Maker</title>
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/auth.css" rel="stylesheet">
    <link href="css/form-validation.css" rel="stylesheet">
</head>
<body class="auth-body">
    <div class="auth-container">
        <div class="auth-card auth-card-wide">
            <div class="auth-header">
                <div class="auth-logo">
                    <span class="auth-icon">🧾</span>
                    <h1 class="auth-title">KMS Receipt Maker</h1>
                </div>
                <h2 class="auth-subtitle" data-lang="register_title">User Registration</h2>
            </div>
            
            <?php if (isset($success)): ?>
                <div class="alert alert-success" role="alert">
                    <?php echo htmlspecialchars($success); ?>
                    <div class="mt-3">
                        <a href="login.php" class="alert-link">Return to Login Page</a>
                        <?php if (strpos($success, 'verification email') !== false): ?>
                            <span class="mx-2">|</span>
                            <a href="#" class="alert-link" onclick="showResendVerificationForm()">Didn't receive email?</a>
                        <?php endif; ?>
                    </div>

                    <?php if (strpos($success, 'verification email') !== false): ?>
                    <div id="resendVerificationForm" style="display: none; margin-top: 15px; padding-top: 15px; border-top: 1px solid #d1ecf1;">
                        <p class="mb-2"><small>Enter your email address to resend verification email:</small></p>
                        <div class="input-group">
                            <input type="email" id="resendEmail" class="form-control form-control-sm" placeholder="Enter your email">
                            <button class="btn btn-outline-success btn-sm" onclick="resendVerificationEmail()">Resend</button>
                        </div>
                        <div id="resendMessage" class="mt-2"></div>
                    </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger" role="alert">
                    <?php foreach ($errors as $error): ?>
                        <div><?php echo htmlspecialchars($error); ?></div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!isset($success)): ?>
            <form method="POST" class="auth-form">
                <!-- Username -->
                <div class="form-group">
                    <label for="username" class="form-label" data-lang="username">Username *</label>
                    <input type="text" class="form-control" id="username" name="username" 
                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                </div>
                
                <!-- Email -->
                <div class="form-group">
                    <label for="email" class="form-label" data-lang="email">Email Address *</label>
                    <input type="email" class="form-control" id="email" name="email" 
                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                </div>
                
                <!-- First Name and Last Name -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="first_name" class="form-label" data-lang="first_name">First Name *</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" 
                                   value="<?php echo htmlspecialchars($_POST['first_name'] ?? ''); ?>" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="last_name" class="form-label" data-lang="last_name">Last Name *</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" 
                                   value="<?php echo htmlspecialchars($_POST['last_name'] ?? ''); ?>" required>
                        </div>
                    </div>
                </div>
                
                <!-- Phone Number -->
                <div class="form-group">
                    <label for="phone" class="form-label" data-lang="phone">Phone Number</label>
                    <input type="tel" class="form-control" id="phone" name="phone" 
                           value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                </div>
                
                <!-- Password -->
                <div class="form-group">
                    <label for="password" class="form-label" data-lang="password">Password *</label>
                    <input type="password" class="form-control" id="password" name="password" required>
                    <div class="password-requirements" id="passwordRequirements">
                        <small class="text-muted" data-lang="password_requirements">密碼要求：</small>
                        <ul class="password-rules">
                            <li id="rule-length" class="rule-item"><span class="rule-icon">✗</span> <span data-lang="password_rule_length">至少8個字符</span></li>
                            <li id="rule-uppercase" class="rule-item"><span class="rule-icon">✗</span> <span data-lang="password_rule_uppercase">包含一個大寫字母</span></li>
                            <li id="rule-number" class="rule-item"><span class="rule-icon">✗</span> <span data-lang="password_rule_number">包含一個數字</span></li>
                            <li id="rule-special" class="rule-item"><span class="rule-icon">✗</span> <span data-lang="password_rule_special">包含一個特殊符號 (!@#$%^&*)</span></li>
                        </ul>
                    </div>
                </div>
                
                <!-- Confirm Password -->
                <div class="form-group">
                    <label for="confirm_password" class="form-label" data-lang="confirm_password">Confirm Password *</label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    <div class="password-match" id="passwordMatch" style="display: none;">
                        <small class="match-message"></small>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary auth-btn" data-lang="register_btn">Register</button>
            </form>
            <?php endif; ?>
            
            <div class="auth-links">
                <a href="login.php" class="auth-link" data-lang="login_link">Already have an account? Login now</a>
            </div>
            
            <div class="language-switcher">
                <button class="kms-lang-btn" onclick="changeLanguage('en')" data-lang-code="en">
                    <span class="flag-icon">🌐</span> English
                </button>
                <button class="kms-lang-btn" onclick="changeLanguage('zh')" data-lang-code="zh">
                    <span class="flag-icon">🌐</span> 中文
                </button>
            </div>
        </div>
    </div>
    
    <script src="js/language.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/form-validation.js"></script>

    <script>
        // 顯示重新發送驗證郵件表單
        function showResendVerificationForm() {
            const form = document.getElementById('resendVerificationForm');
            if (form) {
                form.style.display = form.style.display === 'none' ? 'block' : 'none';
            }
        }

        // 重新發送驗證郵件
        async function resendVerificationEmail() {
            const emailInput = document.getElementById('resendEmail');
            const messageDiv = document.getElementById('resendMessage');
            const button = event.target;

            if (!emailInput || !messageDiv) return;

            const email = emailInput.value.trim();
            if (!email) {
                showResendMessage('Please enter your email address', 'error');
                return;
            }

            // 驗證郵箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                showResendMessage('Please enter a valid email address', 'error');
                return;
            }

            // 顯示加載狀態
            button.disabled = true;
            button.textContent = 'Sending...';

            try {
                const response = await fetch('php/resend_verification.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        language: getCurrentLanguage() || 'en'
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showResendMessage(result.message || 'Verification email sent successfully!', 'success');
                    emailInput.value = '';
                } else {
                    showResendMessage(result.message || 'Failed to send verification email', 'error');
                }

            } catch (error) {
                console.error('Resend verification error:', error);
                showResendMessage('Network error. Please try again later.', 'error');
            } finally {
                button.disabled = false;
                button.textContent = 'Resend';
            }
        }

        // 顯示重新發送消息
        function showResendMessage(message, type) {
            const messageDiv = document.getElementById('resendMessage');
            if (!messageDiv) return;

            messageDiv.className = `mt-2 alert alert-${type === 'success' ? 'success' : 'danger'} alert-sm`;
            messageDiv.textContent = message;
            messageDiv.style.display = 'block';

            // 3秒後自動隱藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    messageDiv.style.display = 'none';
                }, 3000);
            }
        }

        // 獲取當前語言
        function getCurrentLanguage() {
            return localStorage.getItem('language') || 'en';
        }
    </script>
</body>
</html>