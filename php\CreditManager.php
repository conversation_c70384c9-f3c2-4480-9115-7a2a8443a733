<?php
/**
 * 點數管理類
 * KMS Receipt Maker
 */

require_once 'DatabaseMySQLi.php';

class CreditManager {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * 獲取用戶點數餘額
     */
    public function getUserCredits($userId) {
        $sql = "SELECT credits FROM users WHERE id = ?";
        $result = $this->db->fetch($sql, [$userId]);
        return $result ? intval($result['credits']) : 0;
    }
    
    /**
     * 檢查用戶是否有足夠點數
     */
    public function hasEnoughCredits($userId, $requiredCredits) {
        $currentCredits = $this->getUserCredits($userId);
        return $currentCredits >= $requiredCredits;
    }
    
    /**
     * 扣除用戶點數
     */
    public function deductCredits($userId, $amount, $description, $referenceType = null, $referenceId = null) {
        try {
            $this->db->beginTransaction();
            
            // 獲取當前餘額
            $currentCredits = $this->getUserCredits($userId);
            
            // 檢查餘額是否足夠
            if ($currentCredits < $amount) {
                throw new Exception('點數不足，當前餘額: ' . $currentCredits . '，需要: ' . $amount);
            }
            
            // 計算新餘額
            $newBalance = $currentCredits - $amount;
            
            // 更新用戶點數
            $updateSql = "UPDATE users SET credits = ? WHERE id = ?";
            $this->db->execute($updateSql, [$newBalance, $userId]);
            
            // 記錄交易
            $this->recordTransaction(
                $userId,
                'deduct',
                -$amount,
                $currentCredits,
                $newBalance,
                $description,
                $referenceType,
                $referenceId
            );
            
            $this->db->commit();
            
            return [
                'success' => true,
                'balance_before' => $currentCredits,
                'balance_after' => $newBalance,
                'amount_deducted' => $amount
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * 增加用戶點數
     */
    public function addCredits($userId, $amount, $description, $referenceType = null, $referenceId = null, $adminUserId = null) {
        try {
            $this->db->beginTransaction();
            
            // 獲取當前餘額
            $currentCredits = $this->getUserCredits($userId);
            
            // 計算新餘額
            $newBalance = $currentCredits + $amount;
            
            // 更新用戶點數
            $updateSql = "UPDATE users SET credits = ? WHERE id = ?";
            $this->db->execute($updateSql, [$newBalance, $userId]);
            
            // 記錄交易
            $transactionType = $adminUserId ? 'admin_add' : 'add';
            $this->recordTransaction(
                $userId,
                $transactionType,
                $amount,
                $currentCredits,
                $newBalance,
                $description,
                $referenceType,
                $referenceId,
                $adminUserId
            );
            
            $this->db->commit();
            
            return [
                'success' => true,
                'balance_before' => $currentCredits,
                'balance_after' => $newBalance,
                'amount_added' => $amount
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * 記錄點數交易
     */
    private function recordTransaction($userId, $transactionType, $amount, $balanceBefore, $balanceAfter, $description, $referenceType = null, $referenceId = null, $adminUserId = null) {
        $sql = "INSERT INTO credit_transactions (
            user_id, transaction_type, amount, balance_before, balance_after,
            description, reference_type, reference_id, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $this->db->execute($sql, [
            $userId,
            $transactionType,
            $amount,
            $balanceBefore,
            $balanceAfter,
            $description,
            $referenceType,
            $referenceId,
            $adminUserId
        ]);
    }
    
    /**
     * 獲取用戶點數交易記錄
     */
    public function getUserTransactions($userId, $limit = 50, $offset = 0) {
        $sql = "SELECT
            ct.*,
            au.username as admin_username
        FROM credit_transactions ct
        LEFT JOIN users au ON ct.created_by = au.id
        WHERE ct.user_id = ?
        ORDER BY ct.created_at DESC
        LIMIT ? OFFSET ?";

        return $this->db->fetchAll($sql, [$userId, $limit, $offset]);
    }
    
    /**
     * 獲取所有用戶點數交易記錄（管理員用）
     */
    public function getAllTransactions($limit = 100, $offset = 0) {
        $sql = "SELECT
            ct.*,
            u.username,
            au.username as admin_username
        FROM credit_transactions ct
        LEFT JOIN users u ON ct.user_id = u.id
        LEFT JOIN users au ON ct.created_by = au.id
        ORDER BY ct.created_at DESC
        LIMIT ? OFFSET ?";

        return $this->db->fetchAll($sql, [$limit, $offset]);
    }
    
    /**
     * 獲取點數使用統計
     */
    public function getCreditStats($userId = null) {
        $whereClause = $userId ? 'WHERE ct.user_id = ?' : '';
        $params = $userId ? [$userId] : [];
        
        $sql = "SELECT 
            COUNT(*) as total_transactions,
            SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as total_added,
            SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as total_deducted,
            COUNT(CASE WHEN reference_type = 'receipt_save' THEN 1 END) as save_count,
            COUNT(CASE WHEN reference_type = 'receipt_print' THEN 1 END) as print_count
        FROM credit_transactions ct
        {$whereClause}";
        
        return $this->db->fetch($sql, $params);
    }
    
    /**
     * 為新註冊用戶分配免費點數
     */
    public function grantRegistrationBonus($userId, $amount = 10) {
        return $this->addCredits(
            $userId,
            $amount,
            'System Initialization Bonus Credits',
            'registration_bonus'
        );
    }
    
    /**
     * 檢查用戶是否為管理員
     */
    public function isAdmin($userId) {
        $sql = "SELECT user_type FROM users WHERE id = ?";
        $user = $this->db->fetch($sql, [$userId]);
        return $user && $user['user_type'] === 'admin';
    }
    
    /**
     * 管理員調整用戶點數
     */
    public function adminAdjustCredits($adminUserId, $targetUserId, $amount, $description) {
        // 檢查管理員權限
        if (!$this->isAdmin($adminUserId)) {
            throw new Exception('沒有管理員權限');
        }
        
        if ($amount > 0) {
            return $this->addCredits(
                $targetUserId,
                $amount,
                $description,
                'admin_adjustment',
                null,
                $adminUserId
            );
        } else {
            return $this->deductCredits(
                $targetUserId,
                abs($amount),
                $description,
                'admin_adjustment',
                null
            );
        }
    }
}
?>