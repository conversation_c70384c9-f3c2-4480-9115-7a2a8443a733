<?php
session_start();

// 檢查用戶是否已登入
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

require_once 'php/DatabaseMySQLi.php';

$db = new Database();
$conn = $db->getConnection();
$user_id = $_SESSION['user_id'];

// 獲取用戶信息
$stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
$stmt->bind_param('i', $user_id);
$stmt->execute();
$user = $stmt->get_result()->fetch_assoc();

if (!$user) {
    header('Location: logout.php');
    exit();
}

// 處理表單提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        // 檢查用戶權限：只有管理員可以修改個人資料
        if ($user['user_type'] !== 'admin') {
            $errors[] = 'Access denied: Only administrators can modify profile information';
        } else {
            $username = trim($_POST['username'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $first_name = trim($_POST['first_name'] ?? '');
            $last_name = trim($_POST['last_name'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            
            $errors = [];
            
            // 驗證輸入
            if (empty($username)) {
                $errors[] = 'Please enter username';
            } elseif (strlen($username) < 3) {
                $errors[] = 'Username must be at least 3 characters';
            }
            
            if (empty($email)) {
                $errors[] = 'Please enter email address';
            } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $errors[] = 'Please enter a valid email address';
            }
            
            if (empty($first_name)) {
                $errors[] = 'Please enter first name';
            }
            
            if (empty($last_name)) {
                $errors[] = 'Please enter last name';
            }
            
            // 檢查用戶名和郵箱是否已被其他用戶使用
            if (empty($errors)) {
                $stmt = $conn->prepare("SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?");
                $stmt->bind_param('ssi', $username, $email, $user_id);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result->num_rows > 0) {
                    $errors[] = 'Username or email is already in use by another user';
                }
            }
            
            if (empty($errors)) {
                try {
                    $stmt = $conn->prepare("
                        UPDATE users SET username = ?, email = ?, first_name = ?, last_name = ?, phone = ? 
                        WHERE id = ?
                    ");
                    $stmt->bind_param('sssssi', $username, $email, $first_name, $last_name, $phone, $user_id);
                    
                    if ($stmt->execute()) {
                        $_SESSION['username'] = $username; // 更新會話中的用戶名
                        $success = 'Profile updated successfully!';
                        
                        // 重新獲取用戶信息
                        $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
                        $stmt->bind_param('i', $user_id);
                        $stmt->execute();
                        $user = $stmt->get_result()->fetch_assoc();
                    } else {
                        $errors[] = 'Update failed, please try again later';
                    }
                } catch (Exception $e) {
                    $errors[] = 'System error, please try again later';
                }
            }
        }
    } elseif ($action === 'change_password') {
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        $errors = [];
        
        if (empty($current_password)) {
            $errors[] = '<span data-lang="please_enter_current_password">Please enter current password</span>';
        } elseif (!password_verify($current_password, $user['password'])) {
            $errors[] = '<span data-lang="current_password_incorrect">Current password is incorrect</span>';
        }
        
        if (empty($new_password)) {
            $errors[] = '<span data-lang="please_enter_new_password">Please enter new password</span>';
        } elseif (strlen($new_password) < 6) {
            $errors[] = '<span data-lang="new_password_min_length">New password must be at least 6 characters</span>';
        }
        
        if ($new_password !== $confirm_password) {
            $errors[] = '<span data-lang="passwords_do_not_match">New passwords do not match</span>';
        }
        
        if (empty($errors)) {
            try {
                $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
                $stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
                $stmt->bind_param('si', $password_hash, $user_id);
                
                if ($stmt->execute()) {
                    $success = '<span data-lang="password_updated_successfully">Password updated successfully!</span>';
                } else {
                    $errors[] = '<span data-lang="failed_to_update_password">Failed to update password, please try again</span>';
                }
            } catch (Exception $e) {
                $errors[] = '<span data-lang="system_error_try_later">System error, please try again later</span>';
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - KMS Receipt Maker</title>
    <link href="css/bootstrap.css" rel="stylesheet">
    <link href="css/profile.css" rel="stylesheet">
    <script src="js/language.js"></script>
</head>
<body class="profile-body">
    <div class="profile-container">
        <!-- 頂部導航 -->
        <nav class="profile-navbar">
            <div class="profile-nav-brand">
                <span class="profile-icon">👤</span>
                <h1 data-lang="profile_settings">Profile Settings</h1>
            </div>
            <div class="profile-nav-actions">
                <a href="index.php" class="btn btn-outline-primary" data-lang="back_to_home">Back to Home</a>
                <a href="logout.php" class="btn btn-outline-danger" data-lang="logout">Logout</a>
            </div>
        </nav>
        
        <!-- 主要內容 -->
        <div class="profile-content">
            <?php if (isset($success)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($success); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($errors)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php foreach ($errors as $error): ?>
                        <div><?php echo htmlspecialchars($error); ?></div>
                    <?php endforeach; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- 居中容器 -->
            <div class="profile-sections-container">
                <!-- 用戶信息卡片 -->
                <div class="profile-card user-info-card">
                    <div class="user-avatar">
                        <span class="avatar-icon">👤</span>
                    </div>
                    <div class="user-details">
                        <h3><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h3>
                        <p class="user-username">@<?php echo htmlspecialchars($user['username']); ?></p>
                        <p class="user-email"><?php echo htmlspecialchars($user['email']); ?></p>
                        <span class="user-type-badge <?php echo $user['user_type']; ?>" data-lang="<?php echo $user['user_type'] === 'admin' ? 'admin' : 'member'; ?>">
                            <?php echo $user['user_type'] === 'admin' ? 'Admin' : 'Member'; ?>
                        </span>
                    </div>
                    <div class="user-stats">
                        <div class="stat-item">
                            <span class="stat-label" data-lang="registration_date">Registration Date</span>
                            <span class="stat-value"><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label" data-lang="last_login">Last Login</span>
                            <span class="stat-value">
                                <?php echo $user['last_login'] ? date('Y-m-d H:i', strtotime($user['last_login'])) : '<span data-lang="never_logged_in">Never logged in</span>'; ?>
                            </span>
                        </div>
                    </div>
                </div>
                
                <!-- Profile Settings 表單 -->
                <?php if ($user['user_type'] === 'admin'): ?>
                    <!-- 個人資料設定 (僅管理員可見) -->
                    <div class="profile-card">
                        <div class="card-header">
                            <h4 data-lang="profile_settings">Profile Settings</h4>
                        </div>
                        <div class="card-body">
                            <form method="POST" class="profile-form">
                                <input type="hidden" name="action" value="update_profile">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="username" class="form-label" data-lang="username">Username *</label>
                                            <input type="text" class="form-control" id="username" name="username" 
                                                   value="<?php echo htmlspecialchars($user['username']); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="email" class="form-label" data-lang="email_address">Email Address *</label>
                                            <input type="email" class="form-control" id="email" name="email" 
                                                   value="<?php echo htmlspecialchars($user['email']); ?>" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="first_name" class="form-label" data-lang="first_name">First Name *</label>
                                            <input type="text" class="form-control" id="first_name" name="first_name"
                                                   value="<?php echo htmlspecialchars($user['first_name'] ?? ''); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="last_name" class="form-label" data-lang="last_name">Last Name *</label>
                                            <input type="text" class="form-control" id="last_name" name="last_name"
                                                   value="<?php echo htmlspecialchars($user['last_name'] ?? ''); ?>" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="phone" class="form-label" data-lang="phone_number">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone"
                                           value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                                </div>
                                
                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary" data-lang="update_profile">Update Profile</button>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- 普通會員的個人資料顯示 (只讀) -->
                    <div class="profile-card">
                        <div class="card-header">
                            <h4 data-lang="profile_information">Profile Information</h4>
                            <small class="text-muted" data-lang="members_readonly_notice">Members can only modify password. Contact administrator to change other information.</small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" data-lang="username">Username</label>
                                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($user['username']); ?>" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" data-lang="email_address">Email Address</label>
                                        <input type="email" class="form-control" value="<?php echo htmlspecialchars($user['email']); ?>" readonly>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" data-lang="first_name">First Name</label>
                                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($user['first_name']); ?>" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label" data-lang="last_name">Last Name</label>
                                        <input type="text" class="form-control" value="<?php echo htmlspecialchars($user['last_name']); ?>" readonly>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" data-lang="phone_number">Phone Number</label>
                                <input type="tel" class="form-control" value="<?php echo htmlspecialchars($user['phone']); ?>" readonly>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                    
                    <!-- 密碼修改 -->
                    <div class="profile-card">
                        <div class="card-header">
                            <h4 data-lang="change_password">Change Password</h4>
                        </div>
                        <div class="card-body">
                            <form method="POST" class="profile-form">
                                <input type="hidden" name="action" value="change_password">
                                
                                <div class="form-group">
                                    <label for="current_password" class="form-label" data-lang="please_enter_password">Please enter password *</label>
                                    <input type="password" class="form-control" id="current_password" name="current_password" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="new_password" class="form-label" data-lang="new_password">New Password *</label>
                                    <input type="password" class="form-control" id="new_password" name="new_password" required>
                                    <small class="form-text text-muted" data-lang="password_min_length">Password must be at least 6 characters</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="confirm_password" class="form-label" data-lang="confirm_new_password">Confirm New Password *</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>
                                
                                <div class="text-center">
                                    <button type="submit" class="btn btn-warning" data-lang="change_password">Change Password</button>
                                </div>
                            </form>
                        </div>
                    </div>
            </div>
        </div>
    </div>
    
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="js/profile.js"></script>
    <script>
        // Initialize language manager when page loads
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof LanguageManager !== 'undefined') {
                LanguageManager.init();
                LanguageManager.applyLanguage();
            }
        });
    </script>
</body>
</html>