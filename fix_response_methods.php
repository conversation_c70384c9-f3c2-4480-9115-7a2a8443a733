<?php
/**
 * 批量修復 PHP 文件中的 Response 方法調用
 * 將 Response::error() 和 Response::success() 改為 errorAndExit() 和 successAndExit()
 */

$phpDir = __DIR__ . '/php/';
$filesToFix = [
    'get_receipts.php',
    'get_config.php',
    'get_item_categories.php',
    'get_receipt.php',
    'session_check.php',
    'save_receipt.php',
    'print_receipt.php',
    'delete_receipt.php',
    'delete_receipts.php',
    'update_receipt_customer.php',
    'update_payment_method.php',
    'update_pc_parts_order.php',
    'delete_pc_part.php',
    'get_configurations.php',
    'save_configuration.php',
    'delete_configuration.php',
    'manage_configurations.php',
    'init_pc_parts.php',
    'log_activity.php',
    'get_user_stats.php',
    'resend_verification.php',
    'self_delete_account.php',
    'admin_trial_management.php',
    'admin_proxy_login.php'
];

function fixResponseMethods($filePath) {
    if (!file_exists($filePath)) {
        echo "文件不存在: $filePath\n";
        return false;
    }
    
    $content = file_get_contents($filePath);
    $originalContent = $content;
    
    // 替換 Response::error() 為 Response::errorAndExit()
    $content = preg_replace('/Response::error\(/', 'Response::errorAndExit(', $content);
    
    // 替換 Response::success() 為 Response::successAndExit()
    $content = preg_replace('/Response::success\(/', 'Response::successAndExit(', $content);
    
    // 移除多餘的 exit; 語句（因為 errorAndExit 和 successAndExit 已經包含 exit）
    $content = preg_replace('/Response::errorAndExit\([^)]*\);\s*exit;/', 'Response::errorAndExit($1);', $content);
    $content = preg_replace('/Response::successAndExit\([^)]*\);\s*exit;/', 'Response::successAndExit($1);', $content);
    
    if ($content !== $originalContent) {
        file_put_contents($filePath, $content);
        echo "已修復: " . basename($filePath) . "\n";
        return true;
    } else {
        echo "無需修復: " . basename($filePath) . "\n";
        return false;
    }
}

echo "開始批量修復 Response 方法調用...\n\n";

$fixedCount = 0;
foreach ($filesToFix as $file) {
    $filePath = $phpDir . $file;
    if (fixResponseMethods($filePath)) {
        $fixedCount++;
    }
}

echo "\n修復完成！共修復了 $fixedCount 個文件。\n";

// 特殊處理 manage_company_info.php（使用 echo json_encode 的方式）
$specialFile = $phpDir . 'manage_company_info.php';
if (file_exists($specialFile)) {
    $content = file_get_contents($specialFile);
    $originalContent = $content;
    
    // 將 echo json_encode(Response::error(...)) 改為 Response::errorAndExit(...)
    $content = preg_replace('/echo json_encode\(Response::error\(([^)]+)\)\);/', 'Response::errorAndExit($1);', $content);
    
    if ($content !== $originalContent) {
        file_put_contents($specialFile, $content);
        echo "已修復特殊文件: manage_company_info.php\n";
    }
}

echo "\n所有文件修復完成！\n";
?>