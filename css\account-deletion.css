/* 帳號刪除確認界面樣式 */
/* KMS Receipt Maker - Account Deletion Confirmation UI */

.account-deletion-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    animation: fadeIn 0.3s ease-out;
}

.deletion-modal-content {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    border-radius: 20px;
    padding: 0;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(255, 107, 107, 0.4);
    animation: slideInUp 0.4s ease-out;
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.deletion-modal-header {
    background: rgba(0, 0, 0, 0.2);
    padding: 20px 30px;
    border-radius: 20px 20px 0 0;
    text-align: center;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.deletion-modal-header .warning-icon {
    font-size: 4rem;
    color: #fff;
    margin-bottom: 10px;
    display: block;
    animation: pulse 2s infinite;
}

.deletion-modal-header h2 {
    color: #fff;
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.deletion-modal-header .subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    margin-top: 5px;
    font-weight: 400;
}

.deletion-modal-body {
    padding: 30px;
    background: rgba(255, 255, 255, 0.95);
    color: #333;
}

.warning-section {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 2px solid #ffc107;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 25px;
    position: relative;
    overflow: hidden;
}

.warning-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #ff6b6b, #ffc107, #ff6b6b);
    animation: warningStripe 2s linear infinite;
}

.warning-section h3 {
    color: #856404;
    margin: 0 0 15px 0;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.warning-section .warning-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.warning-section .warning-list li {
    padding: 8px 0;
    border-bottom: 1px solid rgba(133, 100, 4, 0.2);
    display: flex;
    align-items: center;
    gap: 10px;
    color: #856404;
    font-weight: 500;
}

.warning-section .warning-list li:last-child {
    border-bottom: none;
}

.warning-section .warning-list li .icon {
    font-size: 1.2rem;
    color: #dc3545;
}

.data-stats {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 2px solid #2196f3;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 25px;
}

.data-stats h3 {
    color: #1565c0;
    margin: 0 0 15px 0;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.7);
    padding: 15px 10px;
    border-radius: 10px;
    border: 1px solid rgba(33, 150, 243, 0.3);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #1565c0;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    color: #1565c0;
    font-weight: 500;
    margin-top: 5px;
}

.confirmation-section {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    border: 2px solid #f44336;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
}

.confirmation-section h3 {
    color: #c62828;
    margin: 0 0 20px 0;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 1rem;
}

.form-group input[type="password"],
.form-group input[type="text"] {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #ddd;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.form-group input[type="password"]:focus,
.form-group input[type="text"]:focus {
    outline: none;
    border-color: #ff6b6b;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.2);
    background: #fff;
}

.confirmation-input {
    font-family: 'Courier New', monospace;
    font-weight: 700;
    text-align: center;
    letter-spacing: 2px;
    color: #c62828;
}

.deletion-modal-footer {
    padding: 25px 30px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 0 0 20px 20px;
    display: flex;
    justify-content: space-between;
    gap: 15px;
    border-top: 2px solid rgba(0, 0, 0, 0.1);
}

.btn-cancel {
    flex: 1;
    padding: 12px 20px;
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-cancel:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
}

.btn-delete {
    flex: 1;
    padding: 12px 20px;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.btn-delete:hover {
    background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
}

.btn-delete:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-delete.loading {
    color: transparent;
}

.btn-delete.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 動畫 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from { 
        opacity: 0; 
        transform: translateY(50px) scale(0.9); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0) scale(1); 
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes warningStripe {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 響應式設計 */
@media (max-width: 768px) {
    .deletion-modal-content {
        width: 95%;
        margin: 10px;
    }
    
    .deletion-modal-header,
    .deletion-modal-body,
    .deletion-modal-footer {
        padding: 20px;
    }
    
    .deletion-modal-header .warning-icon {
        font-size: 3rem;
    }
    
    .deletion-modal-header h2 {
        font-size: 1.5rem;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .deletion-modal-footer {
        flex-direction: column;
    }
    
    .btn-cancel,
    .btn-delete {
        width: 100%;
    }
}
