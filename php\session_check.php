<?php
/**
 * 檢查用戶會話狀態
 * KMS PC Receipt Maker
 */

session_start();

require_once 'DatabaseMySQLi.php';
require_once 'Response.php';

header('Content-Type: application/json; charset=utf-8');

try {
    if (!isset($_SESSION['user_id'])) {
        Response::successAndExit();
    }
    
    $userId = $_SESSION['user_id'];
    
    // 獲取用戶詳細信息
    $db = new Database();
    $conn = $db->getConnection();
    
    $sql = "SELECT id, username, email, first_name, last_name, user_type, status, last_login FROM users WHERE id = ?";
    $user = $db->fetch($sql, [$userId]);
    
    if (!$user) {
        // 用戶不存在，清除會話
        session_destroy();
        Response::errorAndExit('用戶不存在', 404);
    }
    
    if ($user['status'] !== 'active') {
        // 用戶已被禁用，清除會話
        session_destroy();
        Response::errorAndExit('帳號已被禁用', 403);
    }
    
    // 更新最後活動時間
    $updateSql = "UPDATE users SET last_login = NOW() WHERE id = ?";
    $db->execute($updateSql, [$userId]);
    
    // 返回用戶信息
    $userInfo = [
        'logged_in' => true,
        'user_id' => $user['id'],
        'id' => $user['id'],
        'username' => $user['username'],
        'email' => $user['email'],
        'first_name' => $user['first_name'],
        'last_name' => $user['last_name'],
        'full_name' => trim($user['first_name'] . ' ' . $user['last_name']),
        'user_type' => $user['user_type'],
        'is_admin' => $user['user_type'] === 'admin',
        'status' => $user['status'],
        'last_login' => $user['last_login'],
        'session_id' => session_id()
    ];
    
    Response::successAndExit($userInfo, '會話有效');
    
} catch (Exception $e) {
    error_log('Session check error: ' . $e->getMessage());
    Response::errorAndExit('會話檢查失敗: ' . $e->getMessage());
}
?>